import type { Directive, DirectiveBinding } from 'vue'
import processing from '@/commons/processing'

interface CountdownElement extends HTMLElement {
  timer?: number
}

interface CountdownBinding extends DirectiveBinding {
  value: number
  customListeners?: {
    complete?: () => void
  }
}

function updateCountdown(el: CountdownElement, binding: CountdownBinding) {
  let val = +binding.value
  if (!val) {
    return
  }

  const formatter = el.getAttribute('formatter') || 'HH:mm:ss'
  let timer: number | null = null
  
  if (el.timer) {
    window.clearInterval(el.timer)
  }

  const tFunction = () => {
    val -= 1000
    const instance = processing.milliseconds2HMS(val)
    
    if (val <= 0) {
      if (timer) {
        window.clearInterval(timer)
        timer = null
        if (binding.customListeners?.complete) {
          binding.customListeners.complete()
        }
      }
      el.innerHTML = '0'
      return
    }

    el.innerHTML = formatter.replace(/(HH.+?)(mm.+?)(ss.+?)/, (str, $1, $2, $3) => {
      return str
        .replace(new RegExp($1, 'g'), !instance[0] ? '' : $1.replace(/HH/g, processing.toZeroStr(instance[0])))
        .replace(new RegExp($2, 'g'), !instance[0] && !instance[1] ? '' : $2.replace(/mm/g, processing.toZeroStr(instance[1])))
        .replace(new RegExp($3, 'g'), !instance[1] && !instance[2] ? '' : $3.replace(/ss/g, processing.toZeroStr(instance[2])))
    })
  }

  tFunction()
  timer = window.setInterval(tFunction, 1000)
  el.timer = timer
}

const countdown: Directive = {
  beforeMount(el: CountdownElement, binding: CountdownBinding) {
    // In Vue 3, we cannot directly get event listeners from vnode
    // Need to pass event handlers through other ways, like binding.value or custom attributes
    const completeHandler = el.getAttribute('data-complete-handler')
    if (completeHandler) {
      // Event handling logic can be implemented here as needed
      binding.customListeners = {
        complete: () => {
          // Trigger custom event
          const event = new CustomEvent('countdown-complete', {
            detail: { element: el }
          })
          el.dispatchEvent(event)
        }
      }
    }
  },

  mounted(el: CountdownElement, binding: CountdownBinding) {
    updateCountdown(el, binding)
  },

  updated(el: CountdownElement, binding: CountdownBinding) {
    if (binding.oldValue !== binding.value) {
      if (el.timer) {
        window.clearInterval(el.timer)
      }
      updateCountdown(el, binding)
    }
  },

  unmounted(el: CountdownElement, binding: CountdownBinding) {
    if (el.timer) {
      window.clearInterval(el.timer)
      el.timer = undefined
      delete el.timer
    }
    if (binding.customListeners) {
      delete binding.customListeners
    }
  }
}

export default {
  countdown
} 