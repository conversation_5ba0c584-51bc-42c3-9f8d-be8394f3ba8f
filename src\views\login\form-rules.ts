import type { FormRules } from 'element-plus'

export function createFormRules(t: (key: string) => string): FormRules {
  return {
    username: [
      { required: true, message: t('app.common.message.validateNull'), trigger: 'change' },
      { min: 1, max: 20, message: t('app.login.validateMsg.username'), trigger: 'change' }
    ],
    password: [
      { required: true, message: t('app.common.message.validateNull'), trigger: 'change' }
    ],
    email: [
      { required: true, message: t('app.common.message.validateNull'), trigger: 'change' }
    ]
  }
} 