.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.fade-from-above, .fade-from-below, .fade-from-left, .fade-from-right {
  transition: all 0.3s ease-in-out;
}

.fade-from-above-enter, .fade-from-above-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

.fade-from-above-leave-active {
  position: absolute;
}

.fade-from-below-enter, .fade-from-below-leave-to {
  opacity: 0;
  transform: translateY(100%);
}

.fade-from-below-leave-active {
  position: absolute;
}

.fade-from-left-enter, .fade-from-left-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

.fade-from-left-leave-active {
  position: absolute;
}

.fade-from-right-enter, .fade-from-right-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
.fade-from-right-leave-active {
  position: absolute;
}

.slide-left-in-enter, .slide-left-in-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-in-enter-active {
  transition: all 0.3s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.4);
  }
  50% {
    transform: scale(.9);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
