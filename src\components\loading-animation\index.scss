.loading-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  flex-direction: column;
}

.loading-content {
  text-align: center;
}

.loading-logo {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto 20px;
}

.circle-container {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: rotate 4s infinite linear;
}

.circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: pulse 2s infinite ease-in-out;

  &:nth-child(1) {
    border-color: $primary-color;
    animation-delay: calc(var(--i) * 0.2s);
    transform: scale(calc(0.8 + var(--i) * 0.05));
    opacity: calc(1 - var(--i) * 0.15);
  }

  &:nth-child(2) {
    border-color: var(--secondary-color);
    animation-delay: calc(var(--i) * 0.15s);
    transform: scale(0.8 + var(--i) * 0.05);
    opacity: 1 - var(--i) * 0.1;
  }

  &:nth-child(3) {
    border-color: $primary-color;
    border-top-color: var(--secondary-color);
    border-right-color: var(--secondary-color);
    animation-delay: calc(var(--i) * 0.1s);
    transform: scale(0.9 + var(--i) * 0.02);
    opacity: calc(1 - var(--i) * 0.05);
  }

  &:nth-child(4), &:nth-child(5) {
    border-color: $primary-color;
    border-left-color: transparent;
    border-bottom-color: transparent;
    animation-delay: calc(var(--i) * 0.05s);
  }
}

.inner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: bold;
  color: $primary-color;
  letter-spacing: 1px;
  animation: fadeIn 1s ease-in-out;
}

.loading-text {
  margin-top: 20px;
  font-size: 1rem;
  color: var(--text-color);
  letter-spacing: 1px;
  animation: fadeInOut 1.5s infinite ease-in-out;
}

@keyframes rotate {
  0% { transform: rotateZ(0) rotateX(20deg) rotateY(30deg); }
  100% { transform: rotateZ(360deg) rotateX(20deg) rotateY(30deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}
