# 测试指南

本项目采用独立的测试目录结构，将测试文件与源代码文件分开管理，以保持代码库的整洁。

## 测试目录结构

```
tests/
├── unit/               # 单元测试
│   ├── components/     # 组件测试
│   ├── stores/         # 存储测试
│   └── utils/          # 工具函数测试
├── e2e/                # 端到端测试
└── fixtures/           # 测试数据/模拟
```

## 测试技术栈

- **单元测试**: Vitest + Vue Test Utils
- **端到端测试**: Cypress
- **覆盖率报告**: Vitest 内置

## 运行测试

### 单元测试

```bash
# 运行所有单元测试
npm run test:unit

# 运行单个测试文件
npm run test:unit [测试文件路径]

# 带有覆盖率报告
npm run test:unit:coverage
```

### 端到端测试

```bash
# 以开发模式运行 Cypress
npm run test:e2e:dev

# 运行所有端到端测试
npm run test:e2e
```

## 编写测试指南

### 单元测试命名约定

- 测试文件命名：`组件名.spec.ts` 或 `函数名.spec.ts`
- 测试用例描述应清晰表达测试目的

```typescript
// 好的例子
describe('ArticleCard', () => {
  it('正确渲染文章标题和摘要', () => {
    // ...
  })
})

// 避免这样
describe('测试', () => {
  it('works', () => {
    // ...
  })
})
```

### 组件测试最佳实践

1. 使用 `mount` 而非 `shallowMount` 进行完整渲染
2. 对于复杂组件，可以使用 `shallowMount` 单独测试组件逻辑
3. 测试 props、事件、插槽等组件的公共接口
4. 使用 `data-testid` 属性选择元素，避免依赖样式或文本内容

```typescript
// 组件
<button data-testid="submit-btn">提交</button>

// 测试
const button = wrapper.find('[data-testid="submit-btn"]')
```

### 模拟数据和依赖

使用 `tests/fixtures` 中的模拟数据：

```typescript
import { mockArticles } from '@tests/fixtures/articles'

// 在测试中使用
test('显示文章列表', () => {
  // 使用 mockArticles
})
```

### 异步测试

```typescript
it('异步加载数据', async () => {
  // 发起异步请求
  await wrapper.vm.loadData()
  
  // 等待 DOM 更新
  await nextTick()
  
  // 进行断言
  expect(wrapper.find('.data-item').exists()).toBe(true)
})
```

## 持续集成

本项目已配置 CI 流程，每次推送代码时会自动运行测试。测试失败会阻止合并到主分支。

## 测试覆盖率要求

- 单元测试覆盖率目标: 80%
- 重要业务逻辑覆盖率: 90%

定期检查测试覆盖率报告，确保关键代码得到充分测试。 