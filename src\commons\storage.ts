import storage from 'store'
import expirePlugin from 'store/plugins/expire'

// 常量定义 (使用Vue 2的命名约定保持兼容性)
// TOKEN
const X_AUTH_TOKEN = 'FU_AISEARCH_X_AUTH_TOKEN'
// user info
const USER_BASE_INFO = 'LOGIN_INFO'
const ADMIN_INFO = 'ADMIN_ACCOUNT'
const TRIP_REPLAY_INFO = 'FU_AISEARCH_TRIP_REPLAY_INFO'
// language
const LANG = 'FU_AISEARCH_LANG'
const LANG_PACKAGE = 'FU_AISEARCH_LANG_PACKAGE_ROOT'
const SUPPORT_LANGUAGE = 'FU_AISEARCH_SUPPORT_LANGUAGE'
const HTTP_STATUS = 'FU_AISEARCH_HTTP_STATUS_ROOT'
// History
const BOM_HISTORY = 'FU_AISEARCH_BOM_HISTORY_ROOT'
// Menu
const MENU = 'FU_AI<PERSON>ARCH_MENU_ROOT'
// current chat
const CURRENT_CHAT = 'FU_AISEARCH_CURRENT_CHAT'
// chat data
const CHAT_DATA_PREFIX = 'FU_AISEARCH_CHAT_DATA_'
// chat list map
const CHAT_LIST_MAP_PREFIX = 'FU_AISEARCH_CHAT_LIST_MAP_'
// chat cache id
const CHAT_DATA_CAHCE_ID = 'FU_AISEARCH_DATA_CAHCE_ID'
// first login
const FIRST_LOGIN_PREFIX = 'FU_AISEARCH_FIRST_LOGIN'
// APP Version
const APP_VERSION = 'FU-CHATBOT-VERSION'

// 添加插件
storage.addPlugin(expirePlugin)

// 定义类型接口
interface UserBaseInfo {
  userId?: string;
  username?: string;
  avatar?: string;
  operatorId?: string;
  [key: string]: any;
}

interface HttpStatusMessages {
  DEFAULT_ERROR: string;
  [key: string]: string;
}

interface LanguagePackage {
  [key: string]: any;
}

interface BomHistoryData {
  [key: string]: any;
}

interface MenuData {
  [key: string]: any;
}

interface ChatData {
  [key: string]: any;
}

interface ChatListMap {
  [key: string]: any;
}

// 存储类型
type StorageValueType = string | UserBaseInfo | HttpStatusMessages | LanguagePackage | BomHistoryData | MenuData | ChatData | ChatListMap | boolean | null | undefined;

// 存储管理器
const storageManager = {
  // 系统当前语言
  get lang(): string | null {
    return storage.get(LANG) as string | null
  },
  set lang(val: string | null) {
    if (!val) {
      storage.remove(LANG)
    } else {
      storage.set(LANG, val)
    }
  },

  // 国际化语言包
  get langPackage(): LanguagePackage | null {
    return storage.get(LANG_PACKAGE) as LanguagePackage | null
  },
  set langPackage(val: LanguagePackage | null) {
    if (!val) {
      storage.remove(LANG_PACKAGE)
    } else {
      storage.set(LANG_PACKAGE, val)
    }
  },

  // http状态提示语
  get httpStatus(): HttpStatusMessages | null {
    return storage.get(HTTP_STATUS) as HttpStatusMessages | null
  },
  set httpStatus(val: HttpStatusMessages | null) {
    if (!val) {
      storage.remove(HTTP_STATUS)
    } else {
      storage.set(HTTP_STATUS, val)
    }
  },

  // 历史记录
  get bomHistory(): BomHistoryData | null {
    return storage.get(BOM_HISTORY) as BomHistoryData | null
  },
  set bomHistory(val: BomHistoryData | null) {
    if (!val) {
      storage.remove(BOM_HISTORY)
    } else {
      storage.set(BOM_HISTORY, val)
    }
  },

  // token
  get xAuthToken(): string | null {
    return storage.get(X_AUTH_TOKEN) as string | null
  },
  set xAuthToken(val: string | null) {
    if (!val) {
      storage.remove(X_AUTH_TOKEN)
    } else {
      storage.set(X_AUTH_TOKEN, val)
    }
  },

  // 用户基本信息
  get userBaseInfo(): UserBaseInfo | null {
    return storage.get(USER_BASE_INFO) as UserBaseInfo | null
  },
  set userBaseInfo(val: UserBaseInfo | null) {
    if (!val) {
      storage.remove(USER_BASE_INFO)
    } else {
      storage.set(USER_BASE_INFO, val)
    }
  },

  // 管理员信息 (Vue 3 新增)
  get adminInfo(): UserBaseInfo | null {
    return storage.get(ADMIN_INFO) as UserBaseInfo | null
  },
  set adminInfo(val: UserBaseInfo | null) {
    if (!val) {
      storage.remove(ADMIN_INFO)
    } else {
      storage.set(ADMIN_INFO, val)
    }
  },

  // 行程回放信息 (Vue 3 新增)
  get tripReplayInfo(): any {
    return storage.get(TRIP_REPLAY_INFO)
  },
  set tripReplayInfo(val: any) {
    if (!val) {
      storage.remove(TRIP_REPLAY_INFO)
    } else {
      storage.set(TRIP_REPLAY_INFO, val)
    }
  },

  // 支持的语言列表 (Vue 3 新增)
  get supportLanguage(): string[] | null {
    return storage.get(SUPPORT_LANGUAGE) as string[] | null
  },
  set supportLanguage(val: string[] | null) {
    if (!val) {
      storage.remove(SUPPORT_LANGUAGE)
    } else {
      storage.set(SUPPORT_LANGUAGE, val)
    }
  },

  // ========== Chat相关字段 (从Vue 2迁移) ==========

  // 菜单数据
  get menu(): MenuData | null {
    return storage.get(MENU) as MenuData | null
  },
  set menu(val: MenuData | null) {
    if (!val) {
      storage.remove(MENU)
    } else {
      storage.set(MENU, val)
    }
  },

  // 当前聊天
  get currentChat(): ChatData | null {
    return storage.get(CURRENT_CHAT) as ChatData | null
  },
  set currentChat(val: ChatData | null) {
    if (!val) {
      storage.remove(CURRENT_CHAT)
    } else {
      storage.set(CURRENT_CHAT, val)
    }
  },

  // 聊天数据缓存ID
  get chatDataCahceId(): string | null {
    return storage.get(CHAT_DATA_CAHCE_ID) as string | null
  },
  set chatDataCahceId(val: string | null) {
    if (!val) {
      storage.remove(CHAT_DATA_CAHCE_ID)
    } else {
      storage.set(CHAT_DATA_CAHCE_ID, val)
    }
  },

  // 聊天列表映射 (动态key，依赖chatDataCahceId)
  get chatListMap(): ChatListMap | null {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return null
    return storage.get(`${CHAT_LIST_MAP_PREFIX}_${cacheId}`) as ChatListMap | null
  },
  set chatListMap(val: ChatListMap | null) {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return
    const name = `${CHAT_LIST_MAP_PREFIX}_${cacheId}`
    if (!val) {
      storage.remove(name)
    } else {
      storage.set(name, val)
    }
  },

  // 聊天数据 (动态key，依赖chatDataCahceId)
  get chatData(): ChatData | null {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return null
    return storage.get(`${CHAT_DATA_PREFIX}_${cacheId}`) as ChatData | null
  },
  set chatData(val: ChatData | null) {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return
    const name = `${CHAT_DATA_PREFIX}_${cacheId}`
    if (!val) {
      storage.remove(name)
    } else {
      storage.set(name, val)
    }
  },

  // 首次登录状态 (动态key，依赖chatDataCahceId)
  get firstLogin(): boolean | null {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return null
    return storage.get(`${FIRST_LOGIN_PREFIX}_${cacheId}`) as boolean | null
  },
  set firstLogin(val: boolean | null) {
    const cacheId = this.chatDataCahceId
    if (!cacheId) return
    if (val === null || val === undefined) {
      storage.remove(`${FIRST_LOGIN_PREFIX}_${cacheId}`)
    } else {
      storage.set(`${FIRST_LOGIN_PREFIX}_${cacheId}`, val)
    }
  },

  // 应用版本
  get appVersion(): string | null {
    return storage.get(APP_VERSION) as string | null
  },
  set appVersion(val: string | null) {
    if (!val) {
      storage.remove(APP_VERSION)
    } else {
      storage.set(APP_VERSION, val)
    }
  },

  // ========== 工具方法 ==========

  // 设置带过期时间的数据
  setWithExpiration(key: string, value: StorageValueType, expirationInSeconds: number): void {
    if (!value) {
      storage.remove(key)
    } else {
      // @ts-ignore - store插件的expire用法需要三个参数
      storage.set(key, value, new Date().getTime() + expirationInSeconds * 1000)
    }
  },

  // 清除所有存储
  clearAll(): void {
    storage.clearAll()
  },

  // 通用的getter和setter
  get(key: string): StorageValueType {
    return storage.get(key)
  },

  set(key: string, value: StorageValueType): void {
    if (!value) {
      storage.remove(key)
    } else {
      storage.set(key, value)
    }
  },

  remove(key: string): void {
    storage.remove(key)
  }
}

export default storageManager
