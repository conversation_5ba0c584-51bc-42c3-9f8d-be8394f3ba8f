{"name": "fleetup-ai-search", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest --environment jsdom", "test:unit:coverage": "vitest --environment jsdom --coverage", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint .", "format": "prettier --write src/", "generate-sitemap": "tsx scripts/generate-sitemap.ts"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@vueuse/head": "^2.0.0", "aos": "^2.3.4", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "element-plus": "^2.10.0", "gsap": "^3.12.7", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^3.0.1", "postcss": "^8.5.3", "sass": "^1.86.3", "scss": "^0.2.4", "sharp": "^0.34.2", "sitemap": "^8.0.0", "store": "^2.0.12", "swiper": "^11.2.8", "tailwindcss": "^3.4.1", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@iconify/json": "^2.2.337", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/vite": "^4.1.3", "@tsconfig/node22": "^22.0.1", "@types/aos": "^3.0.7", "@types/js-cookie": "^3.0.6", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@types/nprogress": "^0.2.3", "@types/store": "^2.0.5", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/language-plugin-pug": "latest", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cypress": "^14.2.1", "eslint": "^9.22.0", "eslint-plugin-cypress": "^4.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "pug": "^3.0.3", "pug-plain-loader": "^1.1.0", "start-server-and-test": "^2.0.11", "terser": "^5.42.0", "tsx": "^4.19.4", "typescript": "~5.8.0", "unplugin-icons": "^22.1.0", "vite": "^6.2.4", "vite-plugin-html": "^3.2.2", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}