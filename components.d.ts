/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    App: typeof import('./src/App.vue')['default']
    ComponentsAdminInfoDisplay: typeof import('./src/components/admin-info-display/index.vue')['default']
    ComponentsCFooter: typeof import('./src/components/c-footer/index.vue')['default']
    ComponentsCHeader: typeof import('./src/components/c-header/index.vue')['default']
    ComponentsCPicture: typeof import('./src/components/c-picture/index.vue')['default']
    ComponentsCRightMenuBar: typeof import('./src/components/c-right-menu-bar/index.vue')['default']
    ComponentsCScroll: typeof import('./src/components/c-scroll/index.vue')['default']
    ComponentsImageLoader: typeof import('./src/components/Image-loader/index.vue')['default']
    ComponentsLoadingAnimation: typeof import('./src/components/loading-animation/index.vue')['default']
    ComponentsStructuredData: typeof import('./src/components/structured-data/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ExamplesAdminInfoTest: typeof import('./src/examples/admin-info-test.vue')['default']
    ExamplesCScrollTest: typeof import('./src/examples/c-scroll-test.vue')['default']
    ExamplesDirectivesTest: typeof import('./src/examples/directives-test.vue')['default']
    IEpArrowDown: typeof import('~icons/ep/arrow-down')['default']
    IEpArrowRight: typeof import('~icons/ep/arrow-right')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Views404: typeof import('./src/views/404/index.vue')['default']
    ViewsChat: typeof import('./src/views/chat/index.vue')['default']
    ViewsChatComponentsDialogFeedback: typeof import('./src/views/chat/components/dialog-feedback/index.vue')['default']
    ViewsChatComponentsPanelLeft: typeof import('./src/views/chat/components/panel-left/index.vue')['default']
    ViewsChatComponentsPanelMain: typeof import('./src/views/chat/components/panel-main/index.vue')['default']
    ViewsChatComponentsSendMessageBox: typeof import('./src/views/chat/components/send-message-box/index.vue')['default']
    ViewsHome: typeof import('./src/views/home/<USER>')['default']
    ViewsLayouts: typeof import('./src/views/layouts/index.vue')['default']
    ViewsLayoutsDefault: typeof import('./src/views/layouts/default/index.vue')['default']
    ViewsLogin: typeof import('./src/views/login/index.vue')['default']
    ViewsTestCScroll: typeof import('./src/views/test-c-scroll.vue')['default']
    ViewsTestDirectives: typeof import('./src/views/test-directives.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
