<template lang="pug">
.loading-wrapper(v-if="loading")
  .loading-content
    .loading-logo
      .circle-container
        .circle(v-for="i in 5" :key="i" :style="{ '--i': i }")
      .inner-text Tech Blog
    .loading-text {{ loadingText }}
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: true
  },
  duration: {
    type: Number,
    default: 2000
  }
})

const emit = defineEmits(['finished'])

const loadingText = ref('加载中...')
const loadingTexts = ['初始化...', '加载资源...', '准备完毕...']

onMounted(() => {
  // 动态改变加载文字
  let index = 0
  const interval = setInterval(() => {
    loadingText.value = loadingTexts[index]
    index = (index + 1) % loadingTexts.length
  }, props.duration / 3)
  
  // 完成加载
  setTimeout(() => {
    clearInterval(interval)
    emit('finished')
  }, props.duration)
})
</script>

<style lang="scss" scoped>
@use './index.scss' as *;
</style> 