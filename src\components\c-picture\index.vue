<!-- WebpImage.vue -->
<template lang="pug">
picture
  source(:srcset="webpSrc" type="image/webp")
  source(:srcset="fallbackSrc" :type="fallbackType")
  img(:src="fallbackSrc" :alt="alt" loading="lazy")
</template>

<script setup lang="ts">
defineProps({
  webpSrc: String,
  fallbackSrc: String,
  fallbackType: {
    type: String,
    default: 'image/jpeg'
  },
  alt: {
    type: String,
    default: ''
  }
})
</script>
