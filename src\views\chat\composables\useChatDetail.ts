import { ref } from 'vue'
import { markerMessage } from '../api'

export function useChatDetail() {

  const deleteChat = (chatData: any) => {
    // TODO: Implement delete chat functionality
  }

  const markerAnswer = async (type: number, chatData: any, currentChat: string) => {
    const answer = chatData.allMessages[chatData.answerIndex]

    if (answer.flagLoading) return
    if (answer.flag === type) type = 0

    try {
      answer.flagLoading = true
      const params = { flag: type }
      const urlParams = { mid: answer.id, id: currentChat }

      await markerMessage(params, urlParams)
      answer.flag = type
      // TODO: Update local storage if needed
      // this.updateLocalStorage()
    } catch (e) {
      console.error(e)
    } finally {
      answer.flagLoading = false
    }
  }

  const feedback = (chatData: any, dialogFeedbackRef: any) => {
    dialogFeedbackRef?.show(chatData)
  }

  const answerPageChange = (type: 'prev' | 'next', message: any) => {
    const { answerIndex, allMessages } = message

    if (type === 'prev') {
      if (answerIndex === 0) return false
      message.answerIndex = answerIndex - 1
    } else {
      if (answerIndex === allMessages.length - 1) return false
      message.answerIndex = answerIndex + 1
    }
    // TODO: Update local storage if needed
    // this.updateLocalStorage()
    return true
  }

  return {
    deleteChat,
    markerAnswer,
    feedback,
    answerPageChange
  }
}
