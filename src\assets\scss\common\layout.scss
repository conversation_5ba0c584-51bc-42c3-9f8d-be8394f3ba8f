@use "@/assets/scss/utils/constants" as *;
@use "@/assets/scss/utils/mixins/index" as *;

#wrap {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  background-color: #ffffff;
  z-index: 1;

  .cell {
    word-wrap: normal;
    word-break: break-word;
  }
}

.container {
  @include containerWidth();
  margin: 0 auto;
}

.container-wrap {
  position: relative;
  flex: 1;
  height: 100%;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;

  .flex {
    display: flex;
  }
}

.detail-icon {
  width: 31px;
  height: 31px;
  background: #EAAA00;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  svg {
    width: 18px;
    height: 18px;
    fill: white;
  }

  .f-white {
    width: 18px;
    height: 18px;
    color: white;
  }

  .white {
    color: white;
  }
}

.el-date-range-picker .el-date-range-picker__header button:nth-child(1) {
  display: none;
}

html[lang=ar] {
  .cell {
    text-align: right;
  }

  .el-dialog__headerbtn, .el-message-box__headerbtn {
    right: unset;
    left: 20px;
  }

  .el-form-item__error {
    left: unset;
    right: 0;
  }

  .el-date-range-picker .el-picker-panel__body {
    margin-left: unset;
    margin-right: 110px;
  }

  .el-picker-panel [slot=sidebar], .el-picker-panel__sidebar {
    right: 0;
    .el-picker-panel__shortcut {
      text-align: right;
      padding-right: 12px;
      padding-left: unset;
    }
  }

  .el-date-range-picker__header [class*=arrow-left] {
    float: right;
  }

  .el-date-range-picker__header [class*=arrow-right] {
    float: left;
  }

  .el-date-range-picker__content {
    float: right;
  }

  .el-scrollbar__bar.is-vertical {
    left: 2px;
    right: unset;
  }

  .el-select-dropdown__wrap.el-scrollbar__wrap {
    margin-right: unset !important;
    margin-left: -17px;
  }

  .el-table .caret-wrapper {
    left: 3px;
    right: unset;
  }

  .el-date-editor .el-range__icon {
    margin-right: -5px;
    margin-left: unset;
  }

  .el-date-editor .el-range__close-icon {
    margin-left: -5px;
  }

  .el-pagination .btn-next, .el-pagination .btn-prev {
    transform: rotateY(180deg);
  }

  .el-message-box__title, .el-message-box__message p {
    text-align: right;
  }

  .el-message-box__status {
    right: 0;
  }

  .el-message-box__status + .el-message-box__message {
    padding-left: 12px;
    padding-right: 36px;
  }

  .el-message-box__btns {
    text-align: left;
  }

  .el-message-box__btns button:nth-child(2) {
    margin-left: unset;
    margin-right: 10px;
  }

  .el-color-hue-slider.is-vertical {
    float: left!important;
  }

  .el-collapse-item__arrow {
    margin: 0 auto 0 8px;
    transform: rotateY(180deg);
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(90deg);
  }
}

.dialog-height {
  height: 80vh;
}

.pac-container {
  z-index: 9999 !important;
}
