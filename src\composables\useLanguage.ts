import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLanguageStore } from '@/stores/language'
import * as config from '@/conf/'
// import * as UtilLanguage from '@/utils/language'  // Keep original import as comment

export function useLanguage() {
  const { locale } = useI18n()
  const languageStore = useLanguageStore()

  // Computed properties
  const languageIcon = computed(() => {
    return config.language.icons[locale.value]
  })

  const languageAlias = computed(() => {
    return config.language.alias[locale.value]
  })

  const language = computed(() => {
    return languageStore.lang
  })

  const languageConfig = computed(() => {
    return config.language
  })

  // Watch language changes
  watch(language, (val, oldVal) => {
    // console.log(val)
  })

  // Methods
  const changeLanguage = (_lang: string, _doNotMessage?: boolean) => {
    // TODO: Update this to work with Vue 3 context
    // UtilLanguage.changeLanguage(this, _lang, _doNotMessage)
    languageStore.setLang(_lang)
    locale.value = _lang
    console.log('Language changed to:', _lang)
  }

  return {
    languageIcon,
    languageAlias,
    language,
    languageConfig,
    changeLanguage
  }
} 