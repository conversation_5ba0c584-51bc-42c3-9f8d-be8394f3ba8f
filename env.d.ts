/// <reference types="vite/client" />
/// <reference types="vue/macros-global" />
/// <reference types="element-plus/global" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<Record<string, any>, Record<string, any>, any>
  export default component
}

// 声明 Pug 模板
declare module '*.pug' {
  const content: string
  export default content
}

// 声明 SCSS 模块
declare module '*.scss' {
  const content: Record<string, string>
  export default content
}

// 声明图片资源
declare module '*.png' {
  const src: string
  export default src
}

declare module '*.jpg' {
  const src: string
  export default src
}

declare module '*.jpeg' {
  const src: string
  export default src
}

declare module '*.gif' {
  const src: string
  export default src
}

declare module '*.svg' {
  const src: string
  export default src
}

declare module '*.webp' {
  const src: string
  export default src
}

// 环境变量类型声明
interface ImportMetaEnv {
  readonly VITE_API_TITLE: string
  readonly VITE_APP_TITLE: string
  readonly NODE_ENV: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly SSR: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 全局类型声明
declare global {
  interface Window {
    // 如果有全局变量，在这里声明
  }
}
