@use "@/assets/scss/imports.scss" as *;
.chat-panel-left {
  flex: 0 0 15%;

  min-width: 280px;

  background: $color-theme;

  height: 100%;

  display: flex;

  flex-direction: column;
  .new-chat {
    display: flex;

    justify-content: center;

    padding: 20px 0 10px;

    margin-bottom: 15px;
    :deep(.el-button) {
      width: 90%;
    }
    :deep(.el-loading-mask) {
      border-radius: 14px;
      .el-loading-spinner {
        margin-top: -15px;
        .circular {
          width: 30px;
          height: 30px;
        }
      }
    }
  }
  .chat-list-wrapper {
    flex: 1;

    justify-content: stretch;

    color: #fff;
    .chat-list {
      .group-name {
        display: flex;

        justify-content: center;

        align-items: center;

        color: rgba(255, 255, 255, 0.35);

        padding: 0 20px;

        span {
          font-size: 10px;

          padding: 0 15px;
        }
        &::before,
        &::after {
          flex: 1;

          content: '';

          width: 100px;

          height: 1px;

          background: rgba(255, 255, 255, 0.05);
        }
      }
      .group-item {
        display: flex;

        align-items: center;

        justify-content: space-between;
        & + .group-item {
          margin-top: 15px;
        }
      }
      .list {
        padding: 10px;
      }
      .list-item {
        padding: 0 10px;

        justify-content: space-between;

        border-radius: 5px;

        transition-property: all;

        transition-duration: 0.3s;

        transition-timing-function: ease-in-out;

        background: rgba(0, 0, 0, 0.05);
        & + .list-item {
          margin-top: 5px;
        }
        &,
        .name,
        .operate {
          display: flex;

          align-items: center;

          cursor: pointer;
        }
        .name,
        .operate {
          .item {
            padding: 15px 4px;
            &.chat-title {
              padding: 8px 4px;
            }
          }
        }
        .iconfont {
          font-size: 16px;
        }
        .name {
          width: calc(100% - 70px);
          .iconfont {
            flex: 0 0 20px;
          }
          .chat-title {
            flex: 0 0 calc(100% - 20px);
            overflow: hidden;

            white-space: nowrap;

            text-overflow: ellipsis;

            font-size: 12px;
          }
        }
        .operate {
          flex: 0 0 50px;
          .item {
            position: relative;

            top: 0;

            visibility: hidden;

            opacity: 0;

            transition-property: all;

            transition-duration: 0.3s;

            transition-timing-function: ease-in-out;
            &.count {
              cursor: default;

              margin-left: 5px;
            }
            &.animation:hover {
              top: -5px;
            }
          }
        }
        &.active,
        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
        &.active {
          .operate {
            .item {
              opacity: 1;

              visibility: visible;
            }
          }
        }
      }
    }
  }
}
.popover-delete-cont {
  display: flex;

  flex-direction: column;

  justify-content: center;

  align-items: center;
  .operate {
    display: flex;

    justify-content: center;

    margin-top: 10px;
    .item + .item {
      margin-left: 5px;
    }
  }
}
