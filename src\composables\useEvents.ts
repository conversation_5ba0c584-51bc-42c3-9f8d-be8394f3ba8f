import { watch, onUnmounted } from 'vue'
import { useEventStore } from '@/stores/events'
import type { EventPayload } from '@/stores/events'

export function useEvents() {
  const eventStore = useEventStore()
  const eventListeners = new Map<string, () => void>()

  // Emit event (similar to Vue 2's $emit)
  const $emit = (eventName: string, payload: EventPayload = {}) => {
    eventStore.emitEvent(eventName, payload)
  }

  // Listen to event (similar to Vue 2's $on)
  const $on = (eventName: string, handler: (payload: EventPayload) => void) => {
    const stopWatcher = watch(
      () => eventStore.currentEvent,
      (newEvent) => {
        if (newEvent && newEvent.eventName === eventName) {
          handler(newEvent.payload)
        }
      },
      { immediate: true }
    )

    // Save cleanup function
    eventListeners.set(eventName, stopWatcher)

    return stopWatcher
  }

  // Remove listener (similar to Vue 2's $off)
  const $off = (eventName: string) => {
    const stopWatcher = eventListeners.get(eventName)
    if (stopWatcher) {
      stopWatcher()
      eventListeners.delete(eventName)
    }
  }

  // Remove all listeners
  const $offAll = () => {
    eventListeners.forEach((stopWatcher) => {
      stopWatcher()
    })
    eventListeners.clear()
  }

  // Auto cleanup when component unmounts
  onUnmounted(() => {
    $offAll()
  })

  return {
    $emit,
    $on,
    $off,
    $offAll,
    eventStore
  }
} 