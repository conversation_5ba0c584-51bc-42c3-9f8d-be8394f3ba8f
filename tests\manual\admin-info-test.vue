<template lang="pug">
.admin-info-test
  h2 Admin Info Test
  .test-buttons
    el-button(type="primary" @click="fetchAdminInfo" :loading="loading") 获取管理员信息
    el-button(@click="clearAdminInfo") 清空管理员信息
  
  .admin-info-display(v-if="adminInfo && Object.keys(adminInfo).length > 0")
    h3 管理员信息：
    pre {{ JSON.stringify(adminInfo, null, 2) }}
  
  .no-data(v-else)
    p 暂无管理员信息
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCommonStore } from '@/stores/common'
import { ElMessage } from 'element-plus'

const commonStore = useCommonStore()
const loading = ref(false)

// 获取管理员信息的计算属性
const adminInfo = computed(() => commonStore.adminInfo)

// 获取管理员信息
const fetchAdminInfo = async () => {
  try {
    loading.value = true
    await commonStore.getAdminInfo()
    ElMessage.success('获取管理员信息成功')
  } catch (error) {
    ElMessage.error('获取管理员信息失败')
    console.error('Failed to get admin info:', error)
  } finally {
    loading.value = false
  }
}

// 清空管理员信息
const clearAdminInfo = () => {
  commonStore.updateAdminInfo({})
  ElMessage.info('管理员信息已清空')
}
</script>

<style lang="scss" scoped>
.admin-info-test {
  padding: 20px;
  
  .test-buttons {
    margin: 20px 0;
    
    .el-button + .el-button {
      margin-left: 10px;
    }
  }
  
  .admin-info-display {
    margin-top: 20px;
    
    h3 {
      margin-bottom: 10px;
      color: #409eff;
    }
    
    pre {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      overflow-x: auto;
    }
  }
  
  .no-data {
    margin-top: 20px;
    color: #909399;
    text-align: center;
  }
}
</style> 