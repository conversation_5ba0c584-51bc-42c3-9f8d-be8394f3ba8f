import type { Article } from '@/types'

/**
 * Mock article data for testing
 */
export const mockArticles: Article[] = [
  {
    id: '1',
    title: '使用 Vue 3 Composition API 的最佳实践',
    slug: 'vue3-composition-api-best-practices',
    content: '# Vue 3 Composition API 最佳实践\n\n在本文中，我们将探讨使用 Vue 3 Composition API 的最佳实践...',
    summary: 'Vue 3 的 Composition API 提供了更灵活的代码组织方式，本文分享一些最佳实践。',
    coverImage: 'https://example.com/images/vue3.jpg',
    tags: ['Vue', 'JavaScript', '前端'],
    createdAt: '2023-05-15T09:00:00.000Z',
    updatedAt: '2023-05-15T09:00:00.000Z',
    author: {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatars/zhangsan.jpg'
    },
    featured: true
  },
  {
    id: '2',
    title: 'TypeScript 高级类型详解',
    slug: 'typescript-advanced-types',
    content: '# TypeScript 高级类型\n\nTypeScript 提供了许多高级类型功能，比如映射类型、条件类型等...',
    summary: '深入理解 TypeScript 的高级类型系统，提升你的类型编程能力。',
    coverImage: 'https://example.com/images/typescript.jpg',
    tags: ['TypeScript', '前端'],
    createdAt: '2023-06-20T14:30:00.000Z',
    updatedAt: '2023-06-21T10:15:00.000Z',
    author: {
      id: '2',
      name: '李四',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatars/lisi.jpg'
    },
    featured: true
  },
  {
    id: '3',
    title: 'CSS Grid 布局完全指南',
    slug: 'css-grid-complete-guide',
    content: '# CSS Grid 布局完全指南\n\nCSS Grid 是一种强大的二维布局系统，本文将详细介绍其用法...',
    summary: '从基础到高级，全面学习 CSS Grid 布局，掌握现代网页布局技术。',
    coverImage: 'https://example.com/images/css-grid.jpg',
    tags: ['CSS', '前端', '布局'],
    createdAt: '2023-07-05T08:45:00.000Z',
    updatedAt: '2023-07-05T08:45:00.000Z',
    author: {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatars/zhangsan.jpg'
    },
    featured: false
  }
] 