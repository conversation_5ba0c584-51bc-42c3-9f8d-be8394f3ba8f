import { createI18n, useI18n } from 'vue-i18n'
import storage from '@/commons/storage'
import messages from './langs/index'
import type { MessageSchema, AvailableLocales } from '@/types/i18n.types'
import type { ConfigProviderProps } from 'element-plus'
import en from 'element-plus/es/locale/lang/en'
import es from 'element-plus/es/locale/lang/es'
import ar from 'element-plus/es/locale/lang/ar'
import pt from 'element-plus/es/locale/lang/pt'
import { language } from '@/conf'
import type { ExtendedRequestConfig } from '@/utils/request'
import http from '@/utils/request'
import { ElLoading, ElMessage } from 'element-plus'
import { useLanguageStore } from '@/stores/language'

// Default language settings
const DEFAULT_LOCALE: AvailableLocales = language.default as AvailableLocales

// Element Plus language configuration mapping
const elementLocales: Record<string, ConfigProviderProps['locale']> = {
  en_US: en,
  es_ES: es,
  ar_AE: ar,
  pt_PT: pt
}

// Get user language preference from localStorage, or use default language
const savedLocale = localStorage.getItem('locale') as AvailableLocales | null
const locale = savedLocale || (storage.lang as AvailableLocales) || DEFAULT_LOCALE
// Create i18n instance
export const i18n = createI18n<[MessageSchema], AvailableLocales>({
  legacy: false,
  locale: locale,
  fallbackLocale: language.default,
  messages: messages
})

// Set language
export function setLocale (newLocale: AvailableLocales) {
  const THIS_MESSAGES = (storage.langPackage && storage.langPackage[newLocale]) || {}

  if (THIS_MESSAGES.app) {
    setLocaleData(newLocale, THIS_MESSAGES.app)
    return false
  }
  http({
    method: 'get',
    mask: true,
    apiType: 'LOCAL',
    url: 'data/i18n/' + language.data[newLocale]
  } as ExtendedRequestConfig)
    .then((res: any) => {
      setLocaleData(newLocale, res)
    })
    .catch((errCatch: any) => {
      ElMessage.error(i18n.global.t('app.languageChangeTipError'))
    })
  // if (i18n.global.locale) {
  //   // @ts-ignore - vue-i18n typing issue
  //   i18n.global.locale.value = newLocale
  // }
}

export const setLocaleData = async (_lang: any, _messages: any) => {
  const languageStore = useLanguageStore()
  const THIS_MESSAGES = i18n.global.getLocaleMessage(_lang)
  const storageLangPackage = storage.langPackage || {}
  if (i18n.global.locale) {
    // @ts-ignore - vue-i18n typing issue
    i18n.global.locale.value = _lang
  }
  THIS_MESSAGES.app = _messages
  storageLangPackage[_lang].app = _messages
  storage.langPackage = storageLangPackage
  // storage.httpStatus = i18n.global.t('status')
  i18n.global.setLocaleMessage(_lang, THIS_MESSAGES)
  localStorage.setItem('locale', _lang)
  storage.lang = _lang
  document.querySelector('html')?.setAttribute('lang', _lang)
  languageStore.setLang(_lang)
  // setDocumentTitle(vm)
}
export const languageMap = {
  en_US: {
    short: 'en',
    num: 1,
    name: 'English'
  },
  es_ES: {
    short: 'es',
    num: 2,
    name: 'Español'
  },
  pt_PT: {
    short: 'po',
    num: 3,
    name: 'Português'
  },
  ar_AE: {
    short: 'ar',
    num: 5,
    name: 'عربي'
  }
} as const

export function setDocumentTitle (docTitleState: any) {
  let value = ''
  try {
    if (docTitleState && docTitleState.docTzitleI18n) {
      const docTitleDefault = `app.common.documentTitle.${docTitleState.docTitleI18nModule}.default`
      const docTitleI18n = `app.common.documentTitle.${docTitleState.docTitleI18nModule}.${docTitleState.docTitleI18n}`
      value = i18n.global.t(docTitleI18n)
      if (value === docTitleI18n) value = i18n.global.t(docTitleDefault)
      if (value === docTitleDefault) value = ''
    }
    document.title = value || docTitleState.title || docTitleState.default
  } catch (e) {
    console.error('Error setting document title:', e)
    document.title = docTitleState.title || docTitleState.default
  }
}

// Get current Element Plus language configuration
// export function getElementLocale() {
//   const currentLocale = (localStorage.getItem('locale') as AvailableLocales) || DEFAULT_LOCALE
//   return elementLocales[currentLocale] || elementLocales[DEFAULT_LOCALE]
// }

export const getLanguageInfo = (language: string, defaultLang: string) => {
  const hasLang = languageMap.hasOwnProperty(language)
  if (!hasLang && defaultLang !== undefined && defaultLang) language = defaultLang
  const lang = languageMap[language as keyof typeof languageMap]
  // default: en_US
  return lang
}
export default i18n
