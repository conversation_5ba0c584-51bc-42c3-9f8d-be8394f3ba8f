import type { Directive, DirectiveBinding } from 'vue'

const conf: Record<string, string> = {
  default: 'https://us-west-2.console.aws.amazon.com/console/home?region=us-west-2',
  lambdaFunction: 'https://us-west-2.console.aws.amazon.com/lambda/home?region=us-west-2#/functions/'
}

function getLink(type: string, serviceId?: string): string {
  let result = conf.default
  if (type !== 'default' && conf[type] && serviceId) {
    result = conf[type] + serviceId
  }
  return result
}

function updateAwsLink(el: HTMLElement, binding: DirectiveBinding) {
  const linkText = binding.value
  const linkUrl = el.getAttribute('link-url') || ''
  const serviceType = el.getAttribute('service-type') || 'default'
  const serviceId = el.getAttribute('service-id')
  const isLink = linkUrl.includes('http://') || linkUrl.includes('https://')
  
  if (isLink) {
    el.innerHTML = `<a class="f-link" href="${linkUrl}" target="_blank">${linkText} <i class="iconfont icon-link"></i></a>`
  } else {
    el.innerHTML = `<a class="f-link" href="${getLink(serviceType, serviceId || undefined)}" target="_blank">${linkText} <i class="iconfont icon-link"></i></a>`
  }
}

const awsLink: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    updateAwsLink(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    if (binding.oldValue !== binding.value) {
      updateAwsLink(el, binding)
    }
  }
}

export default {
  'aws-link': awsLink
} 