.page-login {
  width: 100%;

  height: 100vh;

  position: relative;

  display: flex;

  flex-direction: column;

  justify-content: center;

  align-items: center; /*background-image url('~@/assets/images/login/bg.jpg')
  background-size cover
  background-position fixed*/
}
.form-login {
  width: 400px;

  margin: 200px auto;

  position: relative;
  .logo {
    width: 80px;

    height: 80px;

    margin-bottom: 10px;
  }
  &-header {
    text-align: center;
    .title {
      font-size: 1.2rem;

      color: #2a2a2a;

      line-height: 180%;
    }
    .summary {
      font-size: 0.875rem;

      color: rgba(0, 0, 0, 0.65);

      line-height: 160%;
    }
  }
  &-wrap {
    position: relative;

    padding: 25px 0 0 0;

    border-radius: 25px;
  }
  :deep(.el-form) {
    margin: 0;
    padding: 10px 20px;
  }
  
  :deep(.el-form-item) {
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 700;
  }
  
  :deep(.el-input__inner) {
    height: 50px;
    line-height: 50px;
    border-radius: 5px;
    box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.15);
  }
  .login-submit {
    width: 100%;

    margin-top: 10px;

    border-radius: 999px;

    height: 50px;

    font-size: 20px;
    :deep(.el-loading-mask) {
      top: -1px;
      bottom: -1px;
      left: -1px;
      right: -1px;
      border-radius: 20px;
      opacity: 0.4;
    }
  }
  .forget-password {
    display: inline-block;

    line-height: 18px;

    color: orangered;
  }
  .copyright {
    margin-top: 50px;

    font-size: 12px;

    color: #8c939d;

    text-align: center;
  }
}
