<template lang="pug">
  .page-login
    .form-login
      .form-login-wrap
        .form-login-header
          img.logo(:src="logo")
          .title {{ t('app.login.title') }}
        el-form(:model="user" :rules="rules" :inline-message="true" ref="loginFormRef")
          el-form-item.is-no-asterisk(prop="username" label="")
            el-input.login(v-model="user.username" placeholder="Account" autocomplete="off")
          el-form-item.is-no-asterisk(prop="password" label="")
            el-input.login(
              type="password"
              v-model="user.password"
              placeholder="Password"
              autocomplete="off"
              @keyup.enter="login"
            )
          el-form-item
            el-button.login-submit(
              type="warning"
              size="large"
              :disabled="loading"
              :loading="loading"
              @click="login"
            ) {{ t('app.login.startNow') }}
          //-el-form-item.f-tac
            router-link.forget-password(:to="{name: 'forgetPassword'}") Forgot password?
      .copyright
        | &copy; 2023 FleetUp. All rights reserved.<span class="divider"> | </span><a href="https://fleetup.com/privacy-policy/" class="d-inline" title="Privacy Policy">Privacy Policy</a><span class="divider"> | </span><a href="https://fleetup.com/terms-of-service/" class="d-inline" title="Terms of service">Terms of service</a>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, type FormInstance } from 'element-plus'
import { useCommonStore } from '@/stores/common'
import { useChatStore } from '@/stores/chat'
import { login as loginApi } from '@/api/user'
import { createFormRules } from './form-rules'
import logo from '@/assets/images/common/logo.png'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Stores
const commonStore = useCommonStore()
const chatStore = useChatStore()

// Form ref
const loginFormRef = ref<FormInstance>()

// Reactive data
const user = ref({
  username: '',
  password: ''
})

const loading = ref(false)

// Computed
const rules = computed(() => createFormRules(t))

// Methods
const login = async () => {
  if (loading.value) return

  loading.value = true

  try {
    // Validate form
    const valid = await loginFormRef.value?.validate()
    if (!valid) return

    // Call login API
    const response = await loginApi(user.value)

    if (response.code === 200) {
      const { data } = response
      // Login success - update stores
      commonStore.login(data)

      // Reset states
      commonStore.resetAll()
      chatStore.resetAll()

      // Show success message
      ElMessage.success(t('app.login.success') || 'Login successful')

      // Redirect
      redirect()
    } else {
      // Login failed
      ElMessage.error(response.message || t('app.login.failed') || 'Login failed')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    ElMessage.error(error.message || t('app.login.error') || 'Login error occurred')
  } finally {
    loading.value = false
  }
}

const redirect = () => {
  const redirectPath = route.query.redirect as string
  if (redirectPath) {
    window.location.replace(redirectPath)
  } else {
    router.push('/')
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
