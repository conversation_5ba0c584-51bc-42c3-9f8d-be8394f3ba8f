import locale from 'element-plus/es/locale/lang/pt'

// default error message
const DEFAULT_ERROR = 'O sistema está ocupado. Por favor, tente novamente mais tarde.'

export default {
  status: {
    DEFAULT_ERROR: DEFAULT_ERROR,
    404: '404 Not Found',
    500: DEFAULT_ERROR,
    700: DEFAULT_ERROR,
    1001: 'Service API entry does not exist',
    1002: 'No access, please contact the administrator',
    1011: 'The account does not exist, or the account is abnormal. Please log in the system again.',
    1012: 'Account is disabled',
    1013: 'User name or password does not match API gateway',
    1101: 'Failed to create verification code',
    1104: 'Picture verification code error',
    1103: 'Graphic verification code used',
    1105: 'Graphics verification code expired'
  },
  ...locale
} 