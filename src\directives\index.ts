import type { App } from 'vue'

// Auto-load all directive files
const directiveModules = import.meta.glob('./*.ts', { eager: true })

// Collect all directives
const directives: Record<string, any> = {}

Object.entries(directiveModules).forEach(([path, module]) => {
  // Exclude current file (index.ts)
  if (path === './index.ts') return
  
  const mod = module as any
  const directiveExports = mod.default || mod
  
  // Merge directives into directives object
  if (directiveExports && typeof directiveExports === 'object') {
    Object.assign(directives, directiveExports)
  } else {
    console.warn(`Invalid directive export from ${path}:`, directiveExports)
  }
})

// 开发环境下显示加载的指令
if (import.meta.env.DEV) {
  // console.log('Loaded directives:', Object.keys(directives))
}

// Function to install all directives
export function setupDirectives(app: App) {
  if (Object.keys(directives).length === 0) {
    console.warn('No directives found to register!')
    return
  }
  
  Object.entries(directives).forEach(([key, directive]) => {
    if (directive && typeof directive === 'object') {
      app.directive(key, directive)
    } else {
      console.warn(`Invalid directive for key "${key}":`, directive)
    }
  })
  
  // 开发环境下显示注册结果
  if (import.meta.env.DEV) {
    // console.log(`Successfully registered ${Object.keys(directives).length} directives`)
  }
}

// Default export
export default directives 