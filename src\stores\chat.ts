import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import storage from '@/commons/storage'
import http from '@/utils/request'  // TODO: Import http module when available
// import APP_CONFS from '@/conf/index.json'  // TODO: Import app configs when available

// Chat types (migrated from types.js)
export const CHAT_TYPES = {
  M_CHAT_LIST_MAP: 'M_CHAT_LIST_MAP',
  M_CHAT_CURRENT_CHAT: 'M_CHAT_CURRENT_CHAT',
  M_CHAT_DATA: 'M_CHAT_DATA',
  M_CHAT_DATA_CACHE_ID: 'M_CHAT_DATA_CACHE_ID',
  M_CHAT_APP_VERSION: 'M_CHAT_APP_VERSION',
  A_CHAT_APP_VERSION: 'A_CHAT_APP_VERSION',
  M_CHAT_DATABASE_VERSION: 'M_CHAT_DATABASE_VERSION',
  M_<PERSON><PERSON>_RESET_ALL: 'M_CHAT_RESET_ALL'
}

// Define interfaces for TypeScript
interface ChatData {
  [key: string]: any[]
}

interface ChatListMap {
  [key: string]: any
}

interface ChatItem {
  id: string
  data: any
}

interface AppVersionData {
  web?: string
  dataset?: string
}

// Default state function (migrated from index.js)
function getDefaultState() {
  return {
    appVersion: storage.appVersion || '',
    chatDatasetVersion: '',
    chatDataCacheId: null,
    chatAccountId: null,
    chatData: storage.chatData || {},
    chatListMap: {},
    currentChat: null,
    // APP_CONF: APP_CONFS[window.location.host] || APP_CONFS['dev.ai.fleetup.net']  // TODO: Uncomment when available
  }
}

// Utility functions (migrated from mutations.js)
function updateMap(data: ChatListMap, id: string, item: any): ChatListMap {
  if (!item) {
    delete data[id]
  } else {
    data[id] = Object.assign({}, data[id], item)
  }
  return data
}

function updateMapByArray(data: ChatData, id: string, arrs: any[]): ChatData {
  if (!arrs) {
    delete data[id]
  } else {
    data[id] = arrs
  }
  return data
}

export const useChatStore = defineStore('chat', () => {
  // State (migrated from Vuex state)
  const defaultState = getDefaultState()
  
  // App version (existing + enhanced)
  const appVersion = ref<string>(defaultState.appVersion)
  
  // Database version (existing + enhanced)
  const chatDatasetVersion = ref<string>(defaultState.chatDatasetVersion)
  
  // Chat data cache ID
  const chatDataCacheId = ref<string | null>(defaultState.chatDataCacheId)
  
  // Chat account ID
  const chatAccountId = ref<string | null>(defaultState.chatAccountId)
  
  // Chat data
  const chatData = ref<ChatData>(defaultState.chatData)
  
  // Chat list map
  const chatListMap = ref<ChatListMap>(defaultState.chatListMap)
  
  // Current active chat
  const currentChat = ref<any>(defaultState.currentChat)
  
  // App configuration
  // const appConf = ref<any>(defaultState.APP_CONF)  // TODO: Uncomment when available
  
  // Getters (computed properties)
  const hasCurrentChat = computed(() => currentChat.value !== null)
  const chatDataKeys = computed(() => Object.keys(chatData.value))
  const chatListMapKeys = computed(() => Object.keys(chatListMap.value))
  
  // Actions (migrated from mutations.js and actions.js)
  
  // Update chat list map
  const updateChatListMap = (payload: ChatItem) => {
    const updateRes = updateMap(chatListMap.value, payload.id, payload.data)
    chatListMap.value = updateRes
    storage.chatListMap = updateRes
  }
  
  // Set current active chat
  const setCurrentChat = (data: any) => {
    currentChat.value = data
    storage.currentChat = data
  }
  
  // Update chat data
  const updateChatData = (payload: { id: string; data: any[] }) => {
    const updateRes = updateMapByArray(chatData.value, payload.id, payload.data)
    chatData.value = updateRes
    storage.chatData = updateRes
  }
  
  // Set chat data cache ID
  const setChatDataCacheId = (cacheId: string | null) => {
    chatDataCacheId.value = cacheId
    storage.chatDataCahceId = cacheId
  }
  
  // Set app version (enhanced from mutations)
  const setAppVersion = (data: AppVersionData) => {
    chatDatasetVersion.value = data.dataset || ''
    const version = (data.web || '') + '.' + (data.dataset || '')
    appVersion.value = version
    storage.appVersion = version
  }
  
  // Set database version
  const setDatabaseVersion = (version: string) => {
    chatDatasetVersion.value = version
    console.log('Database version set to:', version)
  }
  
  // Reset all chat data
  const resetAll = () => {
    const defaultStateValues = getDefaultState()
    appVersion.value = defaultStateValues.appVersion
    chatDatasetVersion.value = defaultStateValues.chatDatasetVersion
    chatDataCacheId.value = defaultStateValues.chatDataCacheId
    chatAccountId.value = defaultStateValues.chatAccountId
    chatData.value = defaultStateValues.chatData
    chatListMap.value = defaultStateValues.chatListMap
    currentChat.value = defaultStateValues.currentChat
    
    // Clear storage
    storage.appVersion = defaultStateValues.appVersion
    storage.chatData = defaultStateValues.chatData
    storage.chatListMap = defaultStateValues.chatListMap
    storage.currentChat = defaultStateValues.currentChat
    storage.chatDataCahceId = defaultStateValues.chatDataCacheId
  }
  
  // Get app version (enhanced from actions.js)
  const getAppVersion = async () => {
    try {
      // TODO: Replace with actual API call when http module is available
      const response = await http({
        url: '/chatservice/version',
        method: 'GET'
      })
      
      if (response.status === 200) {
        setAppVersion(response.data)
        return response
      } else {
        throw new Error('Failed to get app version')
      }
      
      console.log('Getting app version...')
      return appVersion.value
    } catch (error) {
      console.error('Failed to get app version:', error)
      throw error
    }
  }
  
  // Clear chat data for specific ID
  const clearChatData = (id: string) => {
    updateChatData({ id, data: [] })
  }
  
  // Clear chat list map for specific ID
  const clearChatListMap = (id: string) => {
    updateChatListMap({ id, data: null })
  }

  return {
    // State
    appVersion,
    chatDatasetVersion,
    chatDataCacheId,
    chatAccountId,
    chatData,
    chatListMap,
    currentChat,
    
    // Getters
    hasCurrentChat,
    chatDataKeys,
    chatListMapKeys,
    
    // Actions
    updateChatListMap,
    setCurrentChat,
    updateChatData,
    setChatDataCacheId,
    setAppVersion,
    setDatabaseVersion,
    resetAll,
    getAppVersion,
    clearChatData,
    clearChatListMap
  }
})