---
description: 
globs: 
alwaysApply: true
---

# FleetUp Search AI and Analytics AI - 技术栈文档

## 1. 前端 (Frontend)

- **核心框架**: Vue 3 (Composition API)
- **UI 库**: Element Plus (基于 Vue 3 的 Element UI)
- **路由**: Vue Router
- **状态管理**: Pinia (Vue 3 官方推荐)
- **HTTP Client**: Axios
- **构建工具**: Vite
- **CSS**: SCSS
- **国际化**: vue-i18n
- **代码规范**: ESLint, Prettier

## 2. 版本控制 (Version Control)

- **Git**
- **GitHub** (代码托管与协作)

## 3. 开发工具

- **IDE/编辑器**: Cursor, VS Code, Webstorm

- **包管理器**: npm 