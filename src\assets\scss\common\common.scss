@use "@/assets/scss/utils/constants" as *;
@use "@/assets/scss/utils/mixins/index" as *;
@use "./reset";
@use "./layout";
@use "./transition";
@use "./iconfont";

html[dir="rtl"] {
  direction: rtl;
}

html[dir="ltr"] {
  direction: ltr;
}

.f-clearfix:after,
.f-clearfixli li:after {
  display: block;
  clear: both;
  visibility: hidden;
  height: 0;
  overflow: hidden;
  content: ".";
}

.f-clearfix,
.f-clearfixli li {
  zoom: 1;
}

.f-ib {
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}

.f-dn {
  display: none;
}

.f-fn {
  float: none !important;
  clear: left;
}

.f-db {
  display: block;
}

.f-dib {
  display: inline-block !important;
}

.f-fl {
  float: left;
}

.f-fr {
  float: right;
}

.f-pr {
  position: relative;
}

.f-prz {
  position: relative;
  zoom: 1;
}

.f-oh {
  overflow: hidden;
}

.f-ff0 {
  font-family: "arial", "\5b8b\4f53";
}

.f-ff1 {
  font-family: "Microsoft YaHei", "\5fae\8f6f\96c5\9ed1,arial,\5b8b\4f53";
}

.f-fs1 {
  font-size: 12px;
}

.f-fs2 {
  font-size: 14px;
}

.f-fwn {
  font-weight: 400;
}

.f-fwb {
  font-weight: 700;
}

.f-tal {
  text-align: left !important;
}

.f-tac {
  text-align: center !important;
}

.f-tar {
  text-align: right !important;
}

.f-taj {
  text-align: justify;
  text-justify: inter-ideograph;
}

.f-vam,
.f-vama * {
  vertical-align: middle;
}

.f-wsn {
  word-wrap: normal;
  white-space: nowrap;
}

.f-pre {
  overflow: hidden;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.f-wwb {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
}

.f-ti {
  overflow: hidden;
  text-indent: -30000px;
}

.f-ti2 {
  text-indent: 2em;
}

.f-lhn {
  line-height: normal;
}

.f-light {
  color: $yellow;
}

.f-tdu,
.f-tdu:hover {
  text-decoration: underline;
}

.f-tdn,
.f-tdn:hover {
  text-decoration: none;
}

.f-toe {
  overflow: hidden;
  word-wrap: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.f-csp {
  cursor: pointer;
}

.f-csd {
  cursor: default;
}

.f-csh {
  cursor: help;
}

.f-csm {
  cursor: move;
}

.f-usn {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.f-mt10 {
  margin-top: 10px;
}

.f-mt15 {
  margin-top: 15px;
}

.f-mb15 {
  margin-bottom: 15px;
}

.f-mr5 {
  margin-right: 5px;
}

.f-mr10 {
  margin-right: 10px;
}

.f-ml5 {
  margin-left: 5px;
}

.f-mb10 {
  margin-bottom: 10px;
}

.f-mb20 {
  margin-bottom: 20px;
}

.f-mb30 {
  margin-bottom: 30px;
}

.f-bts1 {
  border-top: 1px solid #d8e2e7;
}

.f-bbs1 {
  border-bottom: 1px solid #d8e2e7;
}

.f-nopd {
  padding: 0 !important;
}

.f-nobr {
  border-right: 0 !important;
}

.f-mlr0 {
  margin: 0 !important;
}

.f-plr0 {
  padding: 0 !important;
}

.f-pd0 {
  padding: 0 !important;
}

.f-required {
  color: $red;
}

.f-nobr {
  border-right: 0 !important;
}

.f-number {
  color: $yellow;
}

.f-unit {
  color: $yellow;
}

.f-time {
  color: $gray;
  font-size: 14px;
}

.f-them-color {
  color: $color-theme-brighten;
}

.f-info {
  color: $blue;
}

.f-success {
  color: $green;
}

.f-warn {
  color: $yellow;
}

.f-error {
  color: $red;
}

.f-light-yellow {
  color: #FFBB07;
}

.f-cursor-p {
  cursor: pointer;
}
