# 双重事件系统设计

## 概述

我们实现了一个双重事件系统，既保持了 Vue 3 原生的事件机制，又增加了 Pinia 状态管理的事件系统，提供了更强大和灵活的组件间通信能力。

## 架构设计

```
指令触发事件
    ↓
┌─────────────────┐
│  Vue $emit      │ ← 原生 Vue 事件系统
│  +              │
│  Pinia emitEvent│ ← Pinia 状态事件系统
└─────────────────┘
    ↓
组件可以选择监听任一种或两种事件
```

## 实现方式

### 1. 在指令中同时触发两种事件

```typescript
// src/directives/observe-el-height.ts
const onHeightChange = throttle(function() {
  const height = window.getComputedStyle(el).getPropertyValue('height')
  if (height === recordHeight) return
  recordHeight = height
  
  const emitEventName = binding.value || 'heightChange'
  
  // 方案1: Vue 原生 $emit 事件
  const instance = binding.instance
  if (instance && typeof (instance as any).$emit === 'function') {
    ;(instance as any).$emit(emitEventName, { height, element: el })
  }
  
  // 方案2: Pinia EventStore 事件
  const eventStore = useEventStore()
  eventStore.emitEvent(emitEventName, { height, element: el })
}, 500)
```

### 2. 在组件中选择监听方式

#### 方式A: Vue 模板事件监听（推荐用于简单场景）

```vue
<template lang="pug">
.chat-detail(
  v-observe-el-height="'chatHeightChange'"
  @chatHeightChange="handleHeightChange"
)
</template>

<script setup lang="ts">
function handleHeightChange(payload) {
  console.log('Vue事件 - 高度变化:', payload.height)
}
</script>
```

#### 方式B: Pinia 事件监听（推荐用于复杂场景）

```vue
<script setup lang="ts">
import { useEvents } from '@/composables/useEvents'

const { $on } = useEvents()

onMounted(() => {
  $on('chatHeightChange', (payload) => {
    console.log('Pinia事件 - 高度变化:', payload.height)
  })
})
</script>
```

#### 方式C: 双重监听（最大兼容性）

```vue
<template lang="pug">
.chat-detail(
  v-observe-el-height="'chatHeightChange'"
  @chatHeightChange="handleVueEvent"
)
</template>

<script setup lang="ts">
import { useEvents } from '@/composables/useEvents'

const { $on } = useEvents()

// Vue 事件处理
function handleVueEvent(payload) {
  console.log('Vue事件:', payload)
  processHeightChange(payload)
}

// Pinia 事件监听
onMounted(() => {
  $on('chatHeightChange', (payload) => {
    console.log('Pinia事件:', payload)
    // 可以执行不同的逻辑，或者作为备用方案
  })
})

function processHeightChange(payload) {
  // 统一的处理逻辑
}
</script>
```

## 使用场景对比

| 场景 | Vue 事件 | Pinia 事件 | 推荐方案 |
|------|----------|------------|----------|
| 父子组件通信 | ✅ 简洁 | ⚠️ 过度设计 | Vue 事件 |
| 跨组件通信 | ❌ 不支持 | ✅ 完美支持 | Pinia 事件 |
| 事件历史记录 | ❌ 无 | ✅ 支持 | Pinia 事件 |
| 条件监听 | ⚠️ 复杂 | ✅ 灵活 | Pinia 事件 |
| 调试需求 | ⚠️ 一般 | ✅ 强大 | Pinia 事件 |
| 简单响应 | ✅ 完美 | ⚠️ 复杂 | Vue 事件 |

## 实际应用示例

### 当前项目中的使用

```vue
<!-- panel-main/index.vue -->
<template lang="pug">
.chat-detail(
  v-observe-el-height="chatHeightChangeEventName"
  @chatHeightChange="chatHeightChange"  // Vue 事件监听
)
</template>

<script setup lang="ts">
import { useEvents } from '@/composables/useEvents'

const { $on } = useEvents()

// Vue 事件处理（主要方案）
function chatHeightChange() {
  showBottom.value = scrollRef.value.hasScroll()
}

// Pinia 事件监听（备用/扩展方案）
onMounted(() => {
  $on(chatHeightChangeEventName, (payload) => {
    console.log('[Pinia] Chat height changed:', payload)
    // 可以执行额外的逻辑，如统计、日志等
  })
})
</script>
```

## 优势总结

### ✅ Vue 事件的优势
1. **性能优越**: Vue 原生优化
2. **语法简洁**: 模板中直接 `@eventName`
3. **类型安全**: TypeScript 完整支持
4. **调试友好**: Vue DevTools 原生支持

### ✅ Pinia 事件的优势
1. **跨组件通信**: 不受组件层级限制
2. **事件历史**: 可查看完整事件记录
3. **状态持久化**: 事件状态可持久化
4. **灵活监听**: 可在任何地方监听事件
5. **扩展性强**: 易于添加中间件、过滤器等

### 🎯 双重系统的优势
1. **最大兼容性**: 支持各种使用场景
2. **渐进式升级**: 可以逐步从 Vue 事件迁移到 Pinia 事件
3. **备用机制**: 一种方式失败时另一种作为备用
4. **灵活选择**: 开发者可根据场景选择最适合的方式

## 最佳实践建议

1. **简单场景**: 优先使用 Vue 事件 (`@eventName`)
2. **复杂场景**: 使用 Pinia 事件系统 (`useEvents`)
3. **关键功能**: 使用双重监听确保可靠性
4. **团队协作**: 制定统一的事件命名规范
5. **性能考虑**: 避免在同一组件中重复处理相同事件

## 迁移指南

### 从纯 Vue 事件迁移
```javascript
// 原来：只有 Vue 事件
@eventName="handler"

// 现在：保持 Vue 事件，增加 Pinia 事件能力
@eventName="handler"  // 保持不变
// + 可选的 Pinia 事件监听
```

### 从 DOM 事件迁移
```javascript
// 原来：DOM 事件
el.addEventListener('eventName', handler)

// 现在：Pinia 事件
const { $on } = useEvents()
$on('eventName', handler)
```

这种双重事件系统为项目提供了最大的灵活性和扩展性，既保持了 Vue 的简洁性，又增加了 Pinia 的强大功能！ 