<template lang="pug">
  .chat-panel-left(v-loading="loading" ref="wrapperRef")
    .new-chat(ref="newChatRef")
      el-button(round @click="newChat" v-loading="createLoading" :disabled="createLoading")
        i.el-icon-plus
        span {{ t('app.chat.newChat') }}
    .chat-list-wrapper
      c-scroll(ref="scrollRef" :height="scrollHeight" :wrapStyle="'padding-right:0px'")
        ul.chat-list
          li.goup-item(v-for="item in list" :key="item.id")
            .group-name
              span {{ item.isToday ? t('app.chat.today') : item.timeFormat }}
            ul.list
              li.list-item(
                v-for="(sitem, sindex) in item.list"
                :key="sitem.id"
                v-loading="sitem.operating"
                :class="{'active': currentChat === sitem.id}"
                @click="selectChat(sitem)"
              )
                .name
                  i.item.iconfont.icon-chat
                  i.item.chat-title(v-if="!sitem.contentEditable" :title="sitem.name") {{ sitem.name }}
                  el-input.item.chat-title(
                    v-else
                    size="small"
                    v-model="sitem.nameCache"
                    :ref="(el) => setChatTitleRef(el, sitem.id)"
                    :class="{'editable': sitem.contentEditable}"
                    @input="handletitleInput(sitem)"
                    @blur="editChatTitle(sitem)"
                    @keyup.enter="editChatTitle(sitem)"
                    :autofocus="true"
                  )
                .operate
                  span.item.count(v-if="sitem.contentEditable")
                    i.current {{ sitem.currentByteLen }}
                    | /
                    i.total {{ limit }}
                  i.item.iconfont.icon-edit.animation(@click.stop="toEditChatTitle(sitem)")
                  el-popover(v-if="!sitem.contentEditable" v-model:visible="sitem.showDelete")
                    .popover-delete-cont
                      span {{ t('app.chat.deleteChat') }}
                      .operate
                        el-button.item(size="small" type="primary" @click="deleteChatConfirm(sitem, sindex)") {{ t('app.ui.ok') }}
                    template #reference
                      i.item.iconfont.icon-delete.animation
</template>

<script setup lang="ts">
import CScroll from '@/components/c-scroll/index.vue'
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useCommonStore } from '@/stores/common'
import { useChatStore } from '@/stores/chat'
import { uuid2, verifyCharacterLength } from '@/utils'
import { addChatList, deleteChatList, editChatDetail, getChatList } from '@/views/chat/api'
import moment from 'moment'
// Constants
const CHAT_NUM_LIMIT = 20

// Composables
const { t } = useI18n()
const commonStore = useCommonStore()
const chatStore = useChatStore()

// Emits
const emit = defineEmits<{
  selectChat: [chatData: any]
  deleteChat: [chatData: any]
}>()

// Reactive state
const createLoading = ref(false)
const loading = ref(false)
const list = ref<any[]>([])
const timeFormat = ref('YYYY-MM-DD')
const limit = ref(50)
const scrollHeight = ref('100%')
const chatOriginalList = ref<any[]>([])
const listMap = ref<any>({})
const resizeTimer = ref<number>()

// Refs
const wrapperRef = ref<HTMLElement>()
const newChatRef = ref<HTMLElement>()
const scrollRef = ref<any>()
// Dynamic refs for chat title inputs
const chatTitleRefs = ref<Record<string, any>>({})

// Function to handle dynamic ref setting
const setChatTitleRef = (el: any, id: string) => {
  if (el) {
    chatTitleRefs.value[`chatTitle_${id}`] = el
  } else {
    delete chatTitleRefs.value[`chatTitle_${id}`]
  }
}

// Computed
const userInfo = computed(() => commonStore.userBaseInfo)
const chatListMap = computed(() => chatStore.chatListMap)
const currentChat = computed(() => chatStore.currentChat)

// Methods
const init = async () => {
  await fechChatList()
}

const recalculateTreeHeight = () => {
  if (resizeTimer.value) clearTimeout(resizeTimer.value)
  resizeTimer.value = window.setTimeout(() => {
    if (wrapperRef.value && newChatRef.value) {
      scrollHeight.value = wrapperRef.value.offsetHeight - newChatRef.value.offsetHeight - 50 + 'px'
    }
  }, 400)
}

const fechChatList = async (data?: any) => {
  try {
    loading.value = true
    const res = await getChatList()
    chatOriginalList.value = res
    updateChatList(res)
  } catch (e) {
    console.error(e)
  } finally {
    loading.value = false
  }
}

const formatChatData = (data: any[]) => {
  if (!data) return { arr: [], map: {} }

  const res: any[] = []
  const group: any = {}

  // grouping
  for (let i = 0, len = data.length; i < len; i++) {
    const item = data[i]
    item.time = item.updateTime || item.createTime
    // item.time = item.createTime
    chatStore.updateChatListMap({ id: item.id, data: JSON.parse(JSON.stringify(item)) })
    const time = moment(item.time).local()
    const isToday = time.isSame(moment(), 'day')

    // data initial
    item.loading = false
    item.showDelete = false
    item.nameCache = item.name
    item.contentEditable = false
    item.operating = false
    item.timeForSort = moment(item.time).unix()
    item.currentByteLen = verifyCharacterLength(item.name).count

    // group by day
    const category = isToday ? 'today' : time.format(timeFormat.value)
    group[category] = group[category] || []
    group[category].push(item)
  }

  Object.keys(group).forEach(date => {
    group[date].sort((a: any, b: any) => {
      const at = a.timeForSort, bt = b.timeForSort
      return bt - at
    })
    if (date !== 'today') {
      res.push({
        isGroup: true,
        isToday: false,
        timeFormat: date,
        timeForSort: `${date.replace(/-/g, '')}`,
        list: group[date]
      })
    }
  })
  res.sort((a, b) => {
    const at = a.timeForSort, bt = b.timeForSort
    return bt - at
  })

  // fortmat group
  if (group.today) {
    res.unshift({
      isGroup: true,
      isToday: true,
      list: group.today || []
    })
  }

  return { arr: res, map: group }
}

const newChat = async () => {
  try {
    if (chatOriginalList.value && chatOriginalList.value.length >= CHAT_NUM_LIMIT) {
      ElMessage.warning(t('app.chat.msgTipChatNumLimit', { num: CHAT_NUM_LIMIT }))
      return false
    }

    createLoading.value = true

    // Create new chat data
    const date = moment(new Date()).format(timeFormat.value + ' HH:mm:ss')
    const param = {
      id: uuid2(),
      name: date
    }

    await addChatList(param)
    await fechChatList()
    selectChat(param)
    return param

  } catch (e) {
    console.error(e)
    return false
  } finally {
    createLoading.value = false
  }
}

const updateChatList = (chatData: any[]) => {
  const { arr, map } = formatChatData(chatData)
  list.value = arr
  listMap.value = map
}

const selectChat = (chatData: any) => {
  if (currentChat.value === chatData.id) return false

  chatStore.setCurrentChat(chatData.id)
  emit('selectChat', chatData)
}

const deleteChatConfirm = async (chatData: any, index: number) => {
  try {
    chatData.showDelete = false
    chatData.operating = true

    await deleteChatList({ id: chatData.id })
    await fechChatList()

    chatStore.setCurrentChat('')
    chatStore.updateChatListMap({ id: chatData.id, data: null })

  } catch (e) {
    console.error(e)
  } finally {
    chatData.operating = false
  }

  emit('deleteChat', chatData)
}

const deleteChatCancel = (chatData: any) => {
  chatData.showDelete = false
}

const toEditChatTitle = (chatData: any) => {
  const statusCache = chatData.contentEditable
  chatData.contentEditable = !chatData.contentEditable

  if (statusCache) {
    editChatTitle(chatData)
  } else {
    nextTick(() => {
      const inputRef = chatTitleRefs.value[`chatTitle_${chatData.id}`]
      if (inputRef && inputRef.focus) {
        inputRef.focus()
      }
    })
  }
}

const editChatTitle = async (chatData: any) => {
  if (chatData.contentEditable) {
    chatData.contentEditable = false
  }

  const oldTitle = chatData.name
  const name = chatData.nameCache = chatData.nameCache.trim()

  if (!name) {
    chatData.nameCache = chatData.name
    ElMessage.warning(t('app.chat.titleEmpty'))
    return
  }

  chatData.name = name
  if (name === oldTitle) return false

  try {
    chatData.operating = true

    const { id, updateTime, createTime, status, userId } = chatData
    await editChatDetail({ name: name }, { id: chatData.id })

    chatStore.updateChatListMap({
      id: chatData.id,
      data: { id, name, updateTime, createTime, status, userId }
    })

  } catch (e) {
    console.error(e)
  } finally {
    chatData.operating = false
  }
}

const handletitleInput = (sitem: any) => {
  const str = sitem.nameCache.trim()

  if (!str) {
    sitem.nameCache = ''
    sitem.currentByteLen = 0
    return false
  }

  const res = verifyCharacterLength(str, limit.value)
  if (res.exceed) {
    sitem.nameCache = res.cont
  }
  sitem.currentByteLen = res.count
}

// Lifecycle
onMounted(() => {
  init()

  // scroll height calculate
  nextTick(() => {
    if (wrapperRef.value && newChatRef.value) {
      scrollHeight.value = wrapperRef.value.offsetHeight - newChatRef.value.offsetHeight - 50 + 'px'
    }
  })

  window.addEventListener('resize', recalculateTreeHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', recalculateTreeHeight)
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
  }
})

// Expose methods for parent component
defineExpose({
  newChat,
  fechChatList
})
</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
