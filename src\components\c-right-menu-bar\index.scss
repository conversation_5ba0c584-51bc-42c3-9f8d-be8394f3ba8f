@use "@/assets/scss/utils/constants" as *;
.right-menu-bar {
  display: flex;

  flex-direction: column;

  align-items: center;

  padding: 10px 0;
  .item {
    & + .item {
      margin-top: 10px;
    }
  }
  .item-action {
    padding: 0 10px;

    font-size: 18px;

    color: $SP-layout-header-text-color;

    cursor: pointer;
  }
  .data-version {
    position: absolute;

    bottom: 15px;

    cursor: pointer;
    .icon-data-conversion {
      color: #fff;

      font-size: 22px;
    }
  }
  .user-operate {
    .user-avatar {
      width: 40px;

      height: 40px;

      overflow: hidden;

      background: rgba(255, 255, 255, 0.1);

      display: flex;

      justify-content: center;

      align-items: center;

      img {
        width: 40px;

        height: 40px;
      }
    }
  }
  .popover-parent {
    display: block;

    height: 50px;

    line-height: 40px;

    padding: 10px;

    color: $SP-layout-header-text-color;

    user-select: none;

    box-sizing: border-box;

    border-radius: $SP-border-radius;

    cursor: pointer;
  }
  .popover {
    line-height: 50px;
    .iconfont {
      font-size: 20px;
    }
    :deep(.popover-parent) {
      padding: 5px 5px;
    }
    .el-badge__content.is-fixed {
      top: 5px;
    }
  }
  .current-language-icon {
    cursor: pointer;

    img {
      display: block;

      width: auto;

      height: 35px;
    }
  }
}
