import type { Directive, DirectiveBinding } from 'vue'

const baseUrl = window.location.protocol + '//' + window.location.host + '/'

/**
 * 检测图片是否存在
 * @param url 图片 URL
 * @returns Promise<boolean>
 */
function imageIsExist(url: string): Promise<boolean> {
  return new Promise(resolve => {
    const img = new Image()
    img.onload = function() {
      if ((this as HTMLImageElement).complete === true) {
        resolve(true)
      }
    }
    img.onerror = function() {
      resolve(false)
    }
    img.src = url
  })
}

async function updateAvatar(el: HTMLElement, binding: DirectiveBinding) {
  const imgURL = binding.value // Get image URL
  const defaultImgURL = el.getAttribute('default-img-url') // Get default image
  const elementBaseUrl = el.getAttribute('base-url') || baseUrl // Get default image prefix
  const fullName = el.getAttribute('full-name') || 'none' // Get full name
  const NAME = fullName.substring(0, 1).toUpperCase()

  if (imgURL) {
    const exist = await imageIsExist(`${elementBaseUrl}${imgURL}`)
    if (exist) {
      el.innerHTML = `<img src="${elementBaseUrl}${imgURL}" width="100%" height="100%">`
      return
    }
  }

  if (defaultImgURL) {
    const exist = await imageIsExist(`${elementBaseUrl}${defaultImgURL}`)
    if (exist) {
      el.innerHTML = `<img src="${elementBaseUrl}${defaultImgURL}" width="100%" height="100%">`
      return
    }
  }

  el.innerHTML = `${NAME}`
}

const avatar: Directive = {
  async mounted(el: HTMLElement, binding: DirectiveBinding) {
    await updateAvatar(el, binding)
  },

  async updated(el: HTMLElement, binding: DirectiveBinding) {
    if (binding.oldValue !== binding.value) {
      await updateAvatar(el, binding)
    }
  }
}

export default {
  avatar
} 