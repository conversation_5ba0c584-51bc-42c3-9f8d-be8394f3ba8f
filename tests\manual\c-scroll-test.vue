<template lang="pug">
.c-scroll-test
  h1 CScroll 组件功能测试
  
  .test-section
    h2 基础滚动功能
    .scroll-container
      CScroll(
        ref="scrollRef"
        height="300px"
        scrollbarStyle="border: 1px solid #ddd;"
        wrapStyle="padding: 15px;"
      )
        .content
          div(v-for="n in 50" :key="n" :class="{ highlight: n === highlightLine }") 
            | 这是第 {{ n }} 行内容，用于测试滚动功能
            span.line-info (行高: auto, 内容长度: {{ `这是第 ${n} 行内容，用于测试滚动功能`.length }})
    
    .control-buttons
      el-button(type="primary" @click="scrollToTop") 滚动到顶部
      el-button(type="primary" @click="scrollToBottom") 滚动到底部
      el-button(type="primary" @click="scrollToMiddle") 滚动到中间
      el-button(type="info" @click="updateScroll") 更新滚动
      el-button(type="success" @click="checkHasScroll") 检查是否有滚动
      
  .test-section
    h2 动态内容测试
    .dynamic-controls
      el-button(@click="addContent") 添加内容 ({{ dynamicContent.length }} 项)
      el-button(@click="removeContent" :disabled="dynamicContent.length === 0") 删除内容
      el-button(@click="clearContent") 清空内容
      
    .scroll-container(v-if="dynamicContent.length > 0")
      CScroll(
        ref="dynamicScrollRef"
        height="200px"
        scrollbarStyle="border: 1px solid #67c23a;"
        wrapStyle="padding: 10px;"
      )
        .dynamic-content
          .dynamic-item(v-for="(item, index) in dynamicContent" :key="item.id")
            strong 动态项 #{{ item.id }}:
            span {{ item.content }}
            el-button(size="small" type="danger" @click="removeDynamicItem(index)") 删除
            
  .test-section
    h2 RTL 语言支持测试
    .rtl-controls
      el-button(@click="toggleRTL") {{ isRTL ? '切换到 LTR' : '切换到 RTL' }}
      
    .scroll-container(:class="{ rtl: isRTL }")
      CScroll(
        ref="rtlScrollRef"
        height="200px"
        scrollbarStyle="border: 1px solid #e6a23c;"
        wrapStyle="padding: 10px;"
      )
        .rtl-content(:style="{ direction: isRTL ? 'rtl' : 'ltr' }")
          div(v-for="n in 20" :key="n") 
            | {{ isRTL ? 'النص العربي رقم' : 'English text line' }} {{ n }}
            
  .debug-info
    h3 调试信息
    .info-grid
      .info-item
        strong 滚动容器状态:
        ul
          li 是否有滚动: {{ hasScrollStatus }}
          li 当前语言方向: {{ isRTL ? 'RTL (从右到左)' : 'LTR (从左到右)' }}
          li 动态内容数量: {{ dynamicContent.length }}
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import CScroll from '@/components/c-scroll/index.vue'

const scrollRef = ref<InstanceType<typeof CScroll>>()
const dynamicScrollRef = ref<InstanceType<typeof CScroll>>()
const rtlScrollRef = ref<InstanceType<typeof CScroll>>()

const highlightLine = ref(0)
const hasScrollStatus = ref(false)
const isRTL = ref(false)
const dynamicContent = ref<Array<{ id: number; content: string }>>([])
let nextId = 1

// 基础滚动功能
const scrollToTop = () => {
  scrollRef.value?.scrollTo(0)
  highlightLine.value = 1
  setTimeout(() => { highlightLine.value = 0 }, 2000)
}

const scrollToBottom = () => {
  scrollRef.value?.scrollToBottom()
  highlightLine.value = 50
  setTimeout(() => { highlightLine.value = 0 }, 2000)
}

const scrollToMiddle = () => {
  const wrap = scrollRef.value?.wrap
  if (wrap) {
    const middle = wrap.scrollHeight / 2
    scrollRef.value?.scrollTo(middle)
    highlightLine.value = 25
    setTimeout(() => { highlightLine.value = 0 }, 2000)
  }
}

const updateScroll = () => {
  scrollRef.value?.update()
  ElMessage.success('滚动条已更新')
}

const checkHasScroll = () => {
  const hasScroll = scrollRef.value?.hasScroll()
  hasScrollStatus.value = hasScroll || false
  ElMessage.info(`是否有滚动: ${hasScroll}`)
}

// 动态内容功能
const addContent = () => {
  dynamicContent.value.push({
    id: nextId++,
    content: `这是动态添加的内容项，创建时间: ${new Date().toLocaleTimeString()}`
  })
  
  nextTick(() => {
    dynamicScrollRef.value?.update()
    dynamicScrollRef.value?.scrollToBottom()
  })
}

const removeContent = () => {
  if (dynamicContent.value.length > 0) {
    dynamicContent.value.pop()
    nextTick(() => {
      dynamicScrollRef.value?.update()
    })
  }
}

const clearContent = () => {
  dynamicContent.value = []
  nextTick(() => {
    dynamicScrollRef.value?.update()
  })
}

const removeDynamicItem = (index: number) => {
  dynamicContent.value.splice(index, 1)
  nextTick(() => {
    dynamicScrollRef.value?.update()
  })
}

// RTL 支持测试
const toggleRTL = () => {
  isRTL.value = !isRTL.value
  nextTick(() => {
    rtlScrollRef.value?.update()
  })
}

onMounted(() => {
  // 添加一些初始动态内容
  addContent()
  addContent()
  addContent()
  
  console.log('CScroll test component mounted')
})
</script>

<style lang="scss" scoped>
.c-scroll-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-section {
  margin: 40px 0;
  padding: 24px;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  background: #fafbfc;
}

h1 {
  color: #1f2937;
  text-align: center;
  margin-bottom: 40px;
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  color: #374151;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 12px;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

h3 {
  color: #4b5563;
  font-size: 1.2rem;
  margin-bottom: 16px;
}

.scroll-container {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &.rtl {
    direction: rtl;
  }
  
  .content {
    div {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.3s;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.highlight {
        background-color: #fff3cd;
        border-color: #ffeaa7;
      }
      
      .line-info {
        color: #6c757d;
        font-size: 0.85em;
        margin-left: 10px;
      }
    }
  }
}

.control-buttons,
.dynamic-controls,
.rtl-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin: 20px 0;
}

.dynamic-content {
  .dynamic-item {
    padding: 12px;
    margin: 8px 0;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 10px;
    
    strong {
      color: #495057;
      min-width: 100px;
    }
    
    span {
      flex: 1;
      color: #6c757d;
    }
  }
}

.rtl-content {
  div {
    padding: 10px;
    margin: 5px 0;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dee2e6;
  }
}

.debug-info {
  margin-top: 40px;
  padding: 20px;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 8px;
}

.info-grid {
  .info-item {
    margin: 15px 0;
    
    strong {
      display: block;
      margin-bottom: 8px;
      color: #1976d2;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        padding: 4px 0;
        color: #424242;
        
        &:before {
          content: "▸ ";
          color: #2196f3;
          font-weight: bold;
        }
      }
    }
  }
}
</style> 