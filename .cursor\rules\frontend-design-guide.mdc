---
description: 
globs: 
alwaysApply: true
---
---
description: FleetUp Search AI and Analytics AI的前端设计指南
globs: 托管运输车队业务数据，通过自然语言查询提供智能化分析和搜索组件。
alwaysApply: false
---

# 个人技术分享网站 - 前端设计指南

## 项目目录结构
src/
├── api/         # API 接口
├── assets/      # 静态资源
│   ├── css/     # 样式文件
│   └── images/  # 图片资源
├── components/  # 通用组件
├── composables/ # 组合式函数
├── conf/        # 配置文件
├── i18n/        # 国际化
│   └── langs/   # 语言包
├── plugins/     # 插件
├── router/      # 路由配置
├── stores/      # Pinia 状态管理
├── types/       # TypeScript 类型定义
├── utils/       # 工具函数
├── views/       # 页面视图
├── App.vue      # 根组件
└── main.ts      # 应用入口
├── src/                   # 源代码目录
│   ├── api/               # API接口定义
│   ├── assets/      # 静态资源
│   │   ├── scss/     # 样式文件
│   │   ├── fonts/     # 字体文件
│   │   ├── images/     # 图片资源
│   │   └── images/  # 图片资源
│   ├── components/        # 公共组件
│   ├── composables/       # 组合式函数
│   ├── conf/              # 配置文件
│   ├── i18n/              # 国际化
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面视图
│   ├── commons/           # 公共资源
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── public/                # 公共静态资源
├── plugins/               # 插件目录
├── pre-task/             # 预任务处理
├── docs/                 # 文档
├── tests/                # 测试文件
├── cypress/              # 端到端测试
├── node_modules/         # 依赖包
├── .vscode/              # VS Code配置
├── package.json          # 项目配置和依赖
├── package-lock.json     # 依赖版本锁定
├── vite.config.ts        # Vite配置
├── tsconfig.json         # TypeScript配置
├── tsconfig.app.json     # 应用TypeScript配置
├── tsconfig.node.json    # Node环境TypeScript配置
├── tsconfig.vitest.json  # 测试TypeScript配置
├── postcss.config.js     # PostCSS配置
├── tailwind.config.js    # Tailwind CSS配置
├── gulpfile.js           # Gulp构建配置
├── vitest.config.ts      # Vitest测试配置
├── cypress.config.ts     # Cypress测试配置
├── eslint.config.ts      # ESLint配置
├── .prettierrc.json      # Prettier配置
├── .editorconfig         # 编辑器配置
├── .gitignore           # Git忽略配置
├── .gitattributes       # Git属性配置
├── env.d.ts             # 环境变量类型定义
├── components.d.ts      # 组件类型定义
├── auto-imports.d.ts    # 自动导入类型定义
└── index.html           # HTML入口文件

## 1. 编码规范

- **遵循 Vue 官方风格指南**
  - 组件名称使用多词组合（PascalCase）
  - Props 定义应尽量详细，至少指定类型
  - 组件属性顺序：name, props, emits, setup/data, computed, methods
  - 使用 Composition API 语法
  - Vue 中的template模板必须使用Pug语言书写

- **使用 ESLint 和 Prettier 强制代码风格统一**
  - 基于 Vue 官方推荐配置 `eslint-plugin-vue`
  - 使用 `.eslintrc.js` 和 `.prettierrc.js` 统一团队代码风格
  - 将代码格式化集成到 Git 提交流程（pre-commit hook）

- **命名约定**
  - 组件文件名：PascalCase（如 `ArticleList.vue`）
  - 目录名：kebab-case（如 `common-components`）
  - 变量/函数：camelCase（如 `getUserInfo`）
  - CSS 类名：kebab-case（如 `.nav-item`）
  - Store 模块名：camelCase（如 `userStore`）

- **注释规范**
  - 复杂业务逻辑必须添加注释
  - 公共函数需添加参数说明
  - 使用 JSDoc 风格注释 `/**  */` 
  - CSS 模块分组需加注释

## 2. 组件库使用 (Element Plus)

- **按需引入，减少打包体积**
  ```js
  // vite.config.js
  import { defineConfig } from 'vite'
  import AutoImport from 'unplugin-auto-import/vite'
  import Components from 'unplugin-vue-components/vite'
  import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

  export default defineConfig({
    plugins: [
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
  })
  ```

- **优先使用现有组件，避免重复造轮子**
  - 优先考虑element plus中可以使用的组件
  - 表单控件：使用 `ElInput`, `ElSelect` 等
  - 交互反馈：使用 `ElMessage`, `ElNotification` 等
  - 数据展示：使用 `ElTable`, `ElPagination` 等
  - 弹窗：使用 `ElDialog`, `ElDrawer` 等

- **Element Plus 组件样式覆盖方法**
  ```scss
  // 使用深度选择器
  :deep(.el-input__inner) {
    height: 40px;
  }
  
  // 使用全局 SCSS 变量覆盖
  // styles/element-variables.scss
  @forward 'element-plus/theme-chalk/src/common/var.scss' with (
    $colors: (
      'primary': (
        'base': #3b82f6,
      ),
    )
  );
  ```

- **常用组件统一使用规范**
  - Button：主按钮使用 `type="primary"`，次要按钮使用 `type="default"`
  - Form：统一使用 `label-position="top"` 布局
  - Message：成功提示使用 `ElMessage.success`，错误提示用 `ElMessage.error`
  - Table：默认添加斑马纹 `stripe`，边框 `border` 属性

## 3. 样式与主题

- **使用 SCSS 变量管理颜色、字体、间距等**
  ```scss
  // styles/variables.scss
  $primary-color: #3b82f6;
  $secondary-color: #64748b;
  $text-color: #1f2937;
  $bg-color: #ffffff;
  $border-radius: 4px;
  $font-family: 'Inter', sans-serif;
  $spacing-base: 4px;
  $spacing-md: 16px;
  $spacing-lg: 24px;
  ```

- **主题切换实现方案（CSS 变量 + Body 类名切换）**
  ```scss
  // styles/themes.scss
  
  // 定义 CSS 变量
  :root {
    --bg-color: #ffffff;
    --text-color: #1f2937;
    --border-color: #e5e7eb;
  }
  
  // 暗色主题
  body.dark-theme {
    --bg-color: #1f2937;
    --text-color: #f9fafb;
    --border-color: #4b5563;
  }
  ```
  
  ```js
  // 主题切换逻辑
  function toggleTheme() {
    const isDark = document.body.classList.toggle('dark-theme');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
  }
  
  // 初始化主题
  function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
      document.body.classList.add('dark-theme');
    }
  }
  ```

- **响应式设计断点和实现方式（媒体查询）**
  ```scss
  // 断点变量
  $breakpoints: (
    'sm': 640px,
    'md': 768px,
    'lg': 1024px,
    'xl': 1280px,
    '2xl': 1536px,
  );
  
  // 媒体查询混合宏
  @mixin respond-to($breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    
    @if $value != null {
      @media (min-width: $value) {
        @content;
      }
    }
  }
  
  // 使用示例
  .container {
    padding: 1rem;
    
    @include respond-to('md') {
      padding: 2rem;
    }
    
    @include respond-to('lg') {
      padding: 3rem;
    }
  }
  ```

- **全局 CSS 与组件作用域 CSS (Scoped CSS) 的使用策略**
  - 全局样式：主题变量、通用工具类、重置样式、全局动画
  - 组件样式：每个组件使用 `<style scoped>` 定义局部样式
  - 共享样式：抽取到 mixins 或 utility 类复用

## 4. Layout 布局模式

### 布局组件设计

- **采用嵌套路由实现布局复用**
  - 使用Layout组件作为外层容器
  - 通过子路由动态加载内容区域
  - 支持多种不同风格的布局模板

```js
// 布局组件懒加载
const DefaultLayout = () => import(/* webpackChunkName: "layout-default" */ '@/layouts/DefaultLayout.vue')
const AdminLayout = () => import(/* webpackChunkName: "layout-admin" */ '@/layouts/AdminLayout.vue')
const AuthLayout = () => import(/* webpackChunkName: "layout-auth" */ '@/layouts/AuthLayout.vue')

// 页面组件懒加载
const Home = () => import(/* webpackChunkName: "page-home" */ '@/views/Home.vue')
const ArticleList = () => import(/* webpackChunkName: "page-article-list" */ '@/views/ArticleList.vue')
const ArticleDetail = () => import(/* webpackChunkName: "page-article-detail" */ '@/views/ArticleDetail.vue')
```

### 路由配置示例

```js
// router/modules/main.js
export default function(router) {
  router.addRoute({
    path: '/',
    component: DefaultLayout,
    meta: {
      ignoreAuth: true,
      docTitleI18nModule: 'main'
    },
    children: [
      {
        path: '',
        name: 'home',
        component: Home,
        meta: {
          title: '首页',
          i18n: 'main.home',
          ignoreAuth: true
        }
      },
      {
        path: 'articles',
        name: 'articles',
        component: ArticleList,
        meta: {
          title: '文章列表',
          i18n: 'main.articles',
          ignoreAuth: true
        }
      },
      {
        path: 'articles/:slug',
        name: 'article-detail',
        component: ArticleDetail,
        props: true,
        meta: {
          title: '文章详情',
          i18n: 'main.articleDetail',
          ignoreAuth: true
        }
      }
    ]
  })
}
```

### 多种布局类型

- **默认布局 (DefaultLayout)**
  - 适用于普通访客访问的页面
  - 包含导航栏、内容区和页脚
  - 支持响应式调整

- **管理员布局 (AdminLayout)**
  - 适用于后台管理界面
  - 包含侧边栏菜单、顶部工具栏和内容区
  - 可折叠菜单设计

- **认证布局 (AuthLayout)**
  - 适用于登录、注册等认证页面
  - 简化设计，聚焦于表单
  - 可包含品牌元素和背景

### 布局组件结构

```vue
<!-- DefaultLayout.vue -->
<template>
  <div class="default-layout">
    <header class="layout-header">
      <Navbar />
    </header>
    
    <main class="layout-content">
      <!-- 路由出口 - 子路由内容将显示在这里 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    
    <footer class="layout-footer">
      <Footer />
    </footer>
  </div>
</template>
```

### 路由导航守卫集成

布局组件可以与路由导航守卫结合，实现复杂的导航逻辑：

```js
// 带导航守卫的路由配置示例
export default function(router) {
  router.addRoute({
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'dashboard',
        component: Dashboard,
        meta: {
          title: '控制台',
          requiresAuth: true
        },
        beforeEnter: async (to, from, next) => {
          // 获取用户菜单权限
          const { menus } = await store.state.user.getUserMenus()
          
          // 根据菜单权限决定导航逻辑
          if (menus.length > 0) {
            // 查找第一个可访问的菜单
            const firstAccessibleMenu = menus.find(menu => !menu.isOutLink)
            
            if (firstAccessibleMenu) {
              next({ name: firstAccessibleMenu.name })
            } else {
              next()
            }
          } else {
            next()
          }
        }
      }
    ]
  })
}
```

### 布局数据共享

- 使用Provide/Inject在布局与子组件间共享数据
- 通过Pinia存储布局相关状态（如菜单折叠状态）
- 使用事件总线处理跨组件通信

```js
// layouts/DefaultLayout.vue
import { provide, ref } from 'vue'

export default {
  setup() {
    // 创建布局相关的状态
    const isHeaderFixed = ref(false)
    const layoutTheme = ref('default')
    
    // 提供给子组件使用
    provide('layout', {
      isHeaderFixed,
      layoutTheme,
      setLayoutTheme: (theme) => {
        layoutTheme.value = theme
      }
    })
    
    // ...
  }
}
```

## 5. 状态管理 (Pinia)

- **模块化组织 Store**
  ```
  stores/
  ├── index.js          # 导出所有store
  ├── user.js           # 用户相关状态
  ├── article.js        # 文章相关状态
  ├── comment.js        # 评论相关状态
  └── app.js            # 应用全局状态（如主题、语言）
  ```

- **State, Getter, Action 的职责划分**
  - State: 数据源，保存原始数据
  - Getters: 数据处理和派生计算
  - Actions: 异步操作和复杂逻辑处理

  ```js
  // stores/article.js
  import { defineStore } from 'pinia'
  import { getArticles } from '@/api/article'
  
  export const useArticleStore = defineStore('article', {
    state: () => ({
      articles: [],
      loading: false,
      error: null,
      totalPages: 0,
      currentPage: 1
    }),
    
    getters: {
      featuredArticles: (state) => state.articles.filter(a => a.featured),
      articlesByTag: (state) => (tag) => state.articles.filter(a => a.tags.includes(tag))
    },
    
    actions: {
      async fetchArticles(page = 1, limit = 10) {
        this.loading = true;
        try {
          const { data, totalPages } = await getArticles(page, limit);
          this.articles = data;
          this.totalPages = totalPages;
          this.currentPage = page;
          this.error = null;
        } catch (err) {
          this.error = err.message;
        } finally {
          this.loading = false;
        }
      }
    }
  })
  ```

- **何时使用全局状态，何时使用组件内部状态**
  - 全局状态（Pinia Store）：
    - 多组件共享的数据
    - 页面刷新需要保持的数据
    - 复杂的应用状态逻辑
  - 组件内部状态（ref/reactive）：
    - 仅组件内部使用的临时状态
    - 表单输入等临时UI状态
    - 不需要持久化的数据

## 6. 路由 (Vue Router)

- **模块化路由配置**
  - 采用按模块划分路由配置文件的方式，提高可维护性
  
  ```
  router/
  ├── index.js           # 路由主入口，自动导入所有模块
  ├── routers/           # 路由模块目录
  │   ├── main.js        # 主页相关路由
  │   ├── article.js     # 文章相关路由
  │   ├── user.js        # 用户相关路由
  │   └── admin.js       # 管理后台路由
  ```

  ```js
  // router/index.js - 使用require.context自动导入路由模块
  import { createRouter, createWebHistory } from 'vue-router'
  
  const routes = []
  
  // 使用Webpack的require.context动态导入模块
  // 参数说明：目录、是否递归、匹配文件的正则表达式
  const requireContext = require.context('./routers', false, /^\.\/.*\.js$/)
  
  // 遍历所有匹配的文件
  requireContext.keys().forEach(key => {
    // 获取模块
    const mod = requireContext(key)
    // 调用模块的默认导出函数，传入routes数组
    ;(mod.__esModule && mod.default ? mod.default : mod)(routes)
  })
  
  // 创建路由实例
  const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
    scrollBehavior(to, from, savedPosition) {
      if (savedPosition) {
        return savedPosition
      } else {
        return { top: 0 }
      }
    }
  })
  
  export default router
  ```

  ```js
  // router/routers/main.js - 主页相关路由模块
  export default function(routes) {
    routes.push({
      path: '/',
      name: 'home',
      component: () => import('@/views/Home.vue'),
      meta: { 
        title: '首页',
        ignoreAuth: true 
      }
    })
    
    routes.push({
      path: '/about',
      name: 'about',
      component: () => import('@/views/About.vue'),
      meta: { 
        title: '关于我',
        ignoreAuth: true 
      }
    })
  }
  ```
  
  ```js
  // router/routers/article.js - 文章相关路由模块
  export default function(routes) {
    routes.push({
      path: '/articles',
      name: 'articles',
      component: () => import('@/views/articles/ArticleList.vue'),
      meta: { 
        title: '文章列表',
        ignoreAuth: true 
      }
    })
    
    routes.push({
      path: '/articles/:slug',
      name: 'article-detail',
      component: () => import('@/views/articles/ArticleDetail.vue'),
      props: true,
      meta: { 
        title: '文章详情',
        ignoreAuth: true 
      }
    })
  }
  ```

- **与Layout结合使用**
  
  ```js
  // router/routers/admin.js - 管理后台路由（带布局）
  import AdminLayout from '@/layouts/AdminLayout.vue'
  
  export default function(routes) {
    routes.push({
      path: '/admin',
      component: AdminLayout,
      meta: { 
        requiresAuth: true,
        adminOnly: true
      },
      children: [
        {
          path: '',
          name: 'admin-dashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: { title: '控制台' }
        },
        {
          path: 'posts',
          name: 'admin-posts',
          component: () => import('@/views/admin/Posts.vue'),
          meta: { title: '文章管理' }
        },
        {
          path: 'posts/create',
          name: 'admin-post-create',
          component: () => import('@/views/admin/PostEditor.vue'),
          meta: { title: '创建文章' }
        },
        {
          path: 'posts/edit/:id',
          name: 'admin-post-edit',
          component: () => import('@/views/admin/PostEditor.vue'),
          props: true,
          meta: { title: '编辑文章' }
        }
      ]
    })
  }
  ```

- **路由配置规范（命名路由、懒加载）**

## 7. 国际化 (vue-i18n)

- **语言文件的组织结构**
  ```