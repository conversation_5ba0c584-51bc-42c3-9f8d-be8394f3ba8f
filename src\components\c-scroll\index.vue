<template lang="pug">
  el-scrollbar(
    ref="scrollRef" 
    :style="scrollStyle" 
    :wrap-style="transWrapStyle" 
    v-bind="$attrs"
  )
    slot
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElScrollbar } from 'element-plus'
import { useLanguageStore } from '@/stores/language'

interface Props {
  height?: string
  scrollbarStyle?: string
  wrapStyle?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '',
  scrollbarStyle: '',
  wrapStyle: ''
})

const languageStore = useLanguageStore()
const scrollRef = ref<InstanceType<typeof ElScrollbar>>()

// 计算属性
const scrollStyle = computed(() => {
  const style = props.scrollbarStyle
  const processedStyle = style && !/;$/.test(style) ? style + ';' : style
  return `${processedStyle}height: ${props.height};`
})

const transWrapStyle = computed(() => {
  const wrapStyle = props.wrapStyle
  const processedWrapStyle = wrapStyle && !/;$/.test(wrapStyle) ? wrapStyle + ';' : wrapStyle
  return `overflow-x: auto;${processedWrapStyle}`
})

const wrap = computed(() => {
  return scrollRef.value?.wrapRef
})

// 方法
const update = () => {
  nextTick(() => {
    scrollRef.value?.update()
  })
}

const updateTheWrapperStyleByLanguageChange = () => {
  if (!scrollRef.value) return
  
  const wrapper = scrollRef.value.$el.querySelector('.el-scrollbar__wrap') as HTMLElement
  if (!wrapper) return
  
  const style = wrapper.getAttribute('style') || ''
  if (languageStore.lang === 'ar_AE') {
    wrapper.setAttribute('style', style.replace(/margin-right:/g, 'margin-left:'))
  } else {
    wrapper.setAttribute('style', style.replace(/margin-left:/g, 'margin-right:'))
  }
}

const scrollTo = (height: number, stepDis?: number) => {
  const el = wrap.value
  if (!el) return
  
  animation(height, el, stepDis)
}

const animation = (height: number, el: HTMLElement, step?: number) => {
  const currentTop = el.scrollTop
  let distance = Math.abs(height - currentTop)
  if (!distance) return
  
  const isDown = height - currentTop >= 0
  step = step || 30
  
  const frameFunc = () => {
    if (distance > 0) {
      if (isDown) {
        el.scrollTop += step!
      } else {
        el.scrollTop -= step!
      }
      distance -= step!
      window.requestAnimationFrame(frameFunc)
    } else {
      window.cancelAnimationFrame(timer)
    }
  }
  
  const timer = window.requestAnimationFrame(frameFunc)
}

const scrollToBottom = (step?: number) => {
  const el = wrap.value
  if (!el) return
  
  scrollTo(el.scrollHeight, step)
}

const hasScroll = (): boolean => {
  if (!scrollRef.value || !wrap.value) return false
  
  return wrap.value.scrollHeight > wrap.value.clientHeight
}

// 生命周期
onMounted(() => {
  if (languageStore.lang === 'ar_AE') {
    updateTheWrapperStyleByLanguageChange()
  }
})

// 监听器
watch(
  () => languageStore.lang,
  () => {
    updateTheWrapperStyleByLanguageChange()
  }
)

// 暴露方法给父组件
defineExpose({
  update,
  scrollTo,
  scrollToBottom,
  hasScroll,
  wrap
})
</script>

<style lang="scss">
@use "./index.scss";
</style>
