import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { Base64 } from 'js-base64'
import storage from '@/commons/storage'
import { language } from '@/conf/'
import defaultLogo from '@/assets/images/common/logo.png' // Keep as comment - fix path if needed
import { useChatStore } from '@/stores/chat'
import http from '@/utils/request'

// Define interfaces locally to avoid type export issues
interface UserInfo {
  userId?: string
  userName?: string
  sub?: string
  avatar?: string
  email?: string

  [key: string]: any
}

interface BomHistoryItem {
  name: string
  fullPath: string

  [key: string]: any
}

interface DocumentTitle {
  docTitleI18n?: string
  docTitleI18nModule?: string
  title?: string
  default?: string
}

export const useCommonStore = defineStore('common', () => {
  // ========== STATE ==========

  // User information
  const userBaseInfo = ref<Record<string, any>>(storage.userBaseInfo || {})
  // Admin information
  const adminInfo = ref<Record<string, any>>(storage.adminInfo || {})

  // Admin info initialization status
  const adminInfoInitialized = ref<boolean>(false)

  // Language
  const currentLanguage = ref<string>(storage.lang || language.default)

  // Data dictionary
  const dictData = ref<any[]>((storage.get('dictData') as any[]) || [])

  // Products
  const products = ref<any[]>((storage.get('products') as any[]) || [])

  // Vehicles menu counts
  const subMenuCount = ref<(number | null)[]>([null, null, null, null])

  // System logo
  const sysLogo = ref<string>(defaultLogo) // Use default path

  // Simulator session ID
  const sessionId = ref<string>('')

  // Base root path (current website domain)
  const baseRootPath = ref<string>(window.location.origin)

  // Menu collapse status
  const isMenuCollapse = ref<boolean>((storage.get('isMenuCollapse') as unknown as boolean) || true)

  // Tab history
  const bomHistory = ref<BomHistoryItem[]>((storage.bomHistory as BomHistoryItem[]) || [])

  // Current active tab index
  const bomHistoryActiveIndex = ref<number>(0)

  // Document title
  const documentTitle = ref<DocumentTitle>({
    docTitleI18n: undefined,
    docTitleI18nModule: undefined,
    title: undefined,
    default: 'FleetUp AI'
  })

  // First login status
  const firstLogin = ref<boolean>((storage.get('firstLogin') as unknown as boolean) ?? true)

  // Network status
  const networkSuccess = ref<boolean>(true)

  // ========== COMPUTED ==========

  // Get current browser title based on priority
  const currentBrowserTitle = computed(() => {
    const { docTitleI18n, docTitleI18nModule, title, default: defaultTitle } = documentTitle.value

    // Priority: docTitleI18n > title > default
    if (docTitleI18n && docTitleI18nModule) {
      // TODO: Add i18n translation logic here if needed
      return title || defaultTitle || 'FleetUp AI'
    }

    return title || defaultTitle || 'FleetUp AI'
  })

  // ========== ACTIONS ==========

  // Login action (replaces M_COMMON_LOGIN)
  const login = (payload: any) => {
    const tokenId = payload.id_token
    storage.xAuthToken = tokenId

    try {
      // Parse token to get user info (JWT token decode)
      const _userBaseInfo = JSON.parse(Base64.decode(tokenId.toString().split('.')[1]))
      // Use updateUserBaseInfo to update user info consistently
      updateUserBaseInfo(_userBaseInfo)

      // Update chat data cache ID with operatorId
      if (_userBaseInfo.operatorId) {
        const chatStore = useChatStore()
        chatStore.setChatDataCacheId(_userBaseInfo.operatorId)
      }
    } catch (error) {
      console.error('Failed to decode JWT token:', error)
      // Fallback to empty user info using updateUserBaseInfo
      updateUserBaseInfo({})
    }
  }

  const loginOut = () => {
    storage.xAuthToken = ''
    userBaseInfo.value = {}
  }

  // Update user info (replaces M_COMMON_USER_UPDATE)
  const updateUserBaseInfo = (payload: any) => {
    const userInfo = storage.userBaseInfo || {}
    const result = Object.assign(userInfo, payload)
    userBaseInfo.value = result
    storage.userBaseInfo = result
  }

  // Update admin info (replaces M_ADMIN_INFO)
  const updateAdminInfo = (payload: any) => {
    const data = payload || {}
    adminInfo.value = data
    storage.adminInfo = data
  }

  // Get admin info (replaces A_ADMIN_INFO)
  const getAdminInfo = () => {
    return http({
      method: 'GET',
      url: '/setting/query_client_admin_account_byId.json'
    })
      .then((response) => {
        const data = response || {}
        updateAdminInfo(data)
        adminInfoInitialized.value = true
        return data
      })
      .catch((error) => {
        console.log('Get Admin Info fail: ', error)
        throw error
      })
  }

  // Initialize admin info only once
  const initAdminInfo = async () => {
    if (!adminInfoInitialized.value) {
      try {
        await getAdminInfo()
      } catch (error) {
        console.error('Failed to initialize admin info:', error)
        // Even if it fails, it is marked as initialized to avoid duplicate requests
        adminInfoInitialized.value = true
        throw error
      }
    }
    return adminInfo.value
  }

  // Logout (replaces M_COMMON_LOGOUT) - 增强版本，清理所有认证相关信息
  const logout = () => {
    userBaseInfo.value = {}
    adminInfo.value = {}
    adminInfoInitialized.value = false
    storage.userBaseInfo = {}
    storage.adminInfo = {}
    storage.xAuthToken = null

    // 清理所有认证相关的存储（从userStore迁移的功能）
    localStorage.removeItem('token')

    // 动态导入js-cookie以清理JSESSIONID
    import('js-cookie')
      .then(({ default: jsCookie }) => {
        jsCookie.remove('JSESSIONID')
      })
      .catch(() => {
        // 如果js-cookie导入失败，忽略错误
        console.warn('Failed to import js-cookie for logout cleanup')
      })
  }

  // Change language (replaces M_COMMON_LANGUAGE_CHANGE)
  const changeLanguage = (lang: string) => {
    currentLanguage.value = lang
    storage.lang = lang
  }

  // Set submenu count (replaces M_COMMON_SUBMENU_COUNT)
  const setSubMenuCount = (data: { index: number; val: number | null }) => {
    subMenuCount.value = subMenuCount.value.map((val, index) => {
      if (index === data.index) {
        return data.val
      }
      return val
    })
  }

  // Set logo (replaces M_COMMON_LOGO)
  const setLogo = (logoUrl: string) => {
    sysLogo.value = logoUrl
  }

  // Set session ID (replaces M_COMMON_SESSION_ID)
  const setSessionId = (id: string) => {
    sessionId.value = id
  }

  // Toggle menu collapse (replaces M_COMMON_IS_MENU_COLLAPSE)
  const toggleMenuCollapse = (collapsed: boolean) => {
    isMenuCollapse.value = collapsed
    storage.set('isMenuCollapse', collapsed as any)
  }

  // Add history tab (replaces M_BOM_HISTORY_ADD)
  const addBomHistory = (data: BomHistoryItem) => {
    bomHistory.value.push(data)
    const map: Record<string, boolean> = {}

    // Remove duplicates
    const HISTORY = bomHistory.value.reduce((result: BomHistoryItem[], next) => {
      if (!map[next.name]) {
        map[next.name] = true
        result.push(next)
      }
      return result
    }, [])

    storage.bomHistory = HISTORY
    bomHistory.value = HISTORY
  }

  // Delete history tab (replaces M_BOM_HISTORY_DELETE)
  const deleteBomHistory = (data: {
    index?: number
    callback?: (path: string) => void
    isCurrent?: boolean
  }) => {
    let goPath = '/'

    if (data.index) {
      bomHistory.value.splice(data.index, 1)
    } else {
      bomHistory.value.length > 1 && bomHistory.value.splice(1, bomHistory.value.length - 1)
      goPath = bomHistory.value[0].fullPath
      data.callback && data.callback(goPath)
    }

    const HISTORY = bomHistory.value
    storage.bomHistory = HISTORY
    bomHistory.value = HISTORY

    if (data.isCurrent && data.index === HISTORY.length) {
      goPath = bomHistory.value[HISTORY.length - 1].fullPath
      data.callback && data.callback(goPath)
    } else if (data.isCurrent && data.index && data.index < HISTORY.length) {
      goPath = bomHistory.value[data.index].fullPath
      data.callback && data.callback(goPath)
    }
  }

  // Set active history tab (replaces M_BOM_HISTORY_ACTIVE_INDEX)
  const setBomHistoryActiveIndex = (currentRouter: any) => {
    const HISTORY = bomHistory.value
    HISTORY.forEach((item, index) => {
      if (item.name === currentRouter.name) {
        bomHistoryActiveIndex.value = index
        HISTORY[index] = currentRouter
      }
    })
    storage.bomHistory = HISTORY
    bomHistory.value = HISTORY
  }

  // Set dictionary data (replaces M_COMMON_DICT_DATA)
  const setDictData = (data: any[]) => {
    storage.set('dictData', data)
    dictData.value = data
  }

  // Set document title (replaces M_COMMON_DOCUMENT_TITLE)
  const setDocumentTitle = (payload: DocumentTitle) => {
    // 直接覆盖当前值
    documentTitle.value = payload
  }

  // Set network status (replaces M_COMMON_NETWORK_SUCCESS)
  const setNetworkSuccess = (status: boolean) => {
    networkSuccess.value = status
  }

  // Update first login (replaces M_COMMON_FIRST_LOGIN)
  const updateFirstLogin = (value: boolean) => {
    firstLogin.value = value
    storage.set('firstLogin', value as any)
  }

  // Reset all state (replaces M_COMMON_RESET_ALL)
  const resetAll = () => {
    userBaseInfo.value = {}
    adminInfo.value = {}
    adminInfoInitialized.value = false
    currentLanguage.value = language.default
    dictData.value = []
    products.value = []
    subMenuCount.value = [null, null, null, null]
    sysLogo.value = '/res/logo.png'
    sessionId.value = ''
    baseRootPath.value = window.location.origin
    isMenuCollapse.value = true
    bomHistory.value = []
    bomHistoryActiveIndex.value = 0
    documentTitle.value = {
      docTitleI18n: undefined,
      docTitleI18nModule: undefined,
      title: undefined,
      default: 'FleetUp AI'
    }
    firstLogin.value = true
    networkSuccess.value = true
  }

  return {
    // State
    userBaseInfo,
    adminInfo,
    adminInfoInitialized,
    currentLanguage,
    dictData,
    products,
    subMenuCount,
    sysLogo,
    sessionId,
    baseRootPath,
    isMenuCollapse,
    bomHistory,
    bomHistoryActiveIndex,
    documentTitle,
    firstLogin,
    networkSuccess,

    // Computed
    currentBrowserTitle,

    // Actions
    login,
    loginOut,
    updateUserBaseInfo,
    updateAdminInfo,
    getAdminInfo,
    initAdminInfo,
    logout,
    changeLanguage,
    setSubMenuCount,
    setLogo,
    setSessionId,
    toggleMenuCollapse,
    addBomHistory,
    deleteBomHistory,
    setBomHistoryActiveIndex,
    setDictData,
    setDocumentTitle,
    setNetworkSuccess,
    updateFirstLogin,
    resetAll
  }
})
