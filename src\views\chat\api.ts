import { streamFetch } from '@/utils/stream-fetch'
import http from '@/utils/request'

// Type definitions
export interface ChatListItem {
  id: string
  title: string
  created_at: string
  updated_at: string
  [key: string]: any
}

export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  created_at: string
  [key: string]: any
}

export interface ChatDetail {
  id: string
  title: string
  messages: ChatMessage[]
  [key: string]: any
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T,
  [key: string]: any
}

export interface RequestConfig {
  method?: string
  data?: any
  params?: any
  token?: boolean
  [key: string]: any
}

// Chat list operations
export function getChatList(data?: any, other?: RequestConfig): Promise<ChatListItem[]> {
  const config: RequestConfig = {
    method: 'GET',
    ...other
  }
  return http('/chatbot/dialogs', config)
}

export function addChatList(data: any, other?: RequestConfig): Promise<ApiResponse<ChatListItem>> {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...other
  }
  return http('/chatbot/dialogs', config)
}

export function deleteChatList(urlParams: { id: string }, other?: RequestConfig): Promise<ApiResponse> {
  const config: RequestConfig = {
    method: 'DELETE',
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}`, config)
}

// Chat detail operations
export function getChatDetail(
  urlParams: { id: string },
  data?: any,
  other?: RequestConfig
): Promise<ApiResponse<ChatMessage[]>> {
  const config: RequestConfig = {
    method: 'GET',
    params: data,
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}/messages`, config)
}

export function editChatDetail(
  data: any,
  urlParams: { id: string },
  other?: RequestConfig
): Promise<ApiResponse<ChatDetail>> {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}`, config)
}

// Message operations
export function askQuestions(data: any, other?: RequestConfig): Promise<any> {
  const config = {
    method: 'GET',
    data,
    token: true,
    ...other
  }
  return streamFetch(`/chatservice/streaming-messages`, config).then((res) => {
    const data = res.data
    return Promise.resolve({ msg: data })
  })
}

export function saveMessage(
  data: any,
  urlParams: { id: string },
  other?: RequestConfig
): Promise<ApiResponse<ChatMessage>> {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}/messages`, config)
}

export function markerMessage(
  data: any,
  urlParams: { id: string; mid: string },
  other?: RequestConfig
): Promise<ApiResponse> {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}/messages/${urlParams.mid}`, config)
}

export function feedback(
  data: any,
  urlParams: { id: string; mid: string },
  other?: RequestConfig
): Promise<ApiResponse> {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...other
  }
  return http(`/chatbot/dialogs/${urlParams.id}/messages/${urlParams.mid}`, config)
}
