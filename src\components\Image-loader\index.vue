<template>
  <div class="image-loader" :style="containerStyle">
    <!-- 低质量占位图 -->
    <img 
      v-if="placeholder"
      :src="placeholder" 
      class="placeholder"
      :style="{ opacity: loading ? 1 : 0 }"
      alt="placeholder"
    />
    <!-- 主图片 -->
    <img
      :src="src"
      :alt="alt"
      class="main-image"
      :style="{ opacity: loading ? 0 : 1 }"
      @load="onImageLoaded"
      loading="lazy"
    />
    <!-- 加载动画 -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  src: string;
  alt?: string;
  placeholder?: string;
  width?: string | number;
  height?: string | number;
  aspectRatio?: number;
}

const props = defineProps<Props>()

const loading = ref(true)

const containerStyle = computed(() => {
  const style: Record<string, string> = {
    position: 'relative',
    overflow: 'hidden'
  }

  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  } else if (props.aspectRatio) {
    style.paddingBottom = `${(1 / props.aspectRatio) * 100}%`
  }

  return style
})

const onImageLoaded = () => {
  loading.value = false
}
</script>

<style scoped>
.image-loader {
  background-color: #f5f5f5;
}

.placeholder,
.main-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 