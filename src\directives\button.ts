import type { Directive, DirectiveBinding } from 'vue'

interface ButtonElement extends HTMLElement {
  __vueClickOutside__?: (e: Event) => void
}

const button123: Directive = {
  beforeMount(el: ButtonElement, binding: DirectiveBinding) {
    function clickHandler(e: Event) {
      // Check if the clicked element is the element itself, if so, return
      if (el.contains(e.target as Node)) {
        el.setAttribute('disabled', 'true')
        setTimeout(() => {
          el.removeAttribute('disabled')
        }, 1000)
        return false
      }
      
      // Check if a function is bound in the directive
      if (typeof binding.value === 'function') {
        binding.value(e)
      }
    }
    
    // Bind a private variable to the current element for easy event listener removal in unmounted
    el.__vueClickOutside__ = clickHandler
    document.addEventListener('click', clickHandler)
  },
  
  unmounted(el: ButtonElement) {
    if (el.__vueClickOutside__) {
      document.removeEventListener('click', el.__vueClickOutside__)
      delete el.__vueClickOutside__
    }
  }
}

export default {
  'button123': button123
} 