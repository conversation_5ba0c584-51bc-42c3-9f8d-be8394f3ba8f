import type { Directive, DirectiveBinding } from 'vue'

function updateLink(el: HTMLElement, binding: DirectiveBinding) {
  const linkText = binding.value
  const text = el.getAttribute('text') || linkText
  el.innerHTML = `<a class="f-link" href="${linkText}" target="_blank">${text} <i class="iconfont icon-link"></i></a>`
}

const link: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    updateLink(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    if (binding.oldValue !== binding.value) {
      updateLink(el, binding)
    }
  }
}

export default {
  link
} 