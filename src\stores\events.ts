import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

export interface EventPayload {
  [key: string]: any
}

export interface EventState {
  eventName: string
  payload: EventPayload
  timestamp: number
}

export const useEventStore = defineStore('events', () => {
  // Event state
  const currentEvent = ref<EventState | null>(null)
  
  // Event history (optional, for debugging)
  const eventHistory = reactive<EventState[]>([])
  
  // Emit event (replacement for $emit)
  const emitEvent = (eventName: string, payload: EventPayload = {}) => {
    const eventState: EventState = {
      eventName,
      payload,
      timestamp: Date.now()
    }
    
    // Update current event state
    currentEvent.value = eventState
    
    // Add to history
    eventHistory.push(eventState)
    
    // Keep history under 100 entries
    if (eventHistory.length > 100) {
      eventHistory.shift()
    }
    
    console.log(`[EventStore] Emitted event: ${eventName}`, payload)
  }
  
  // Clear current event (optional)
  const clearCurrentEvent = () => {
    currentEvent.value = null
  }
  
  // Get latest event of specific type
  const getLatestEvent = (eventName: string): EventState | null => {
    return eventHistory
      .filter(event => event.eventName === eventName)
      .pop() || null
  }
  
  return {
    currentEvent,
    eventHistory,
    emitEvent,
    clearCurrentEvent,
    getLatestEvent
  }
}) 