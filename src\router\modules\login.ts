import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Login = () => import('@/views/login/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    name: 'login',
    path: '/login',
    component: DefaultLayout,
    redirect: { name: 'loginIndex' },
    meta: {
      ignoreAuth: true,
      docTitleI18nModule: 'login'
    },
    children: [
      {
        path: '',
        name: 'loginIndex',
        meta: {
          title: 'Login',
          i18n: 'login',
          docTitleI18n: 'default',
          ignoreAuth: true,
          ignoreSaveHistory: true
        },
        component: Login
      }
    ]
  })
}
