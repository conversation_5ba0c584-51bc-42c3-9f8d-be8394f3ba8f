<template lang="pug">
  .send-wrapper
    .regenerate(v-if="showRegenerate")
      el-button.regenerate-btn(round @click="regenerate")
        i.iconfont.icon-refresh
        span {{ loading ? t('app.chat.responding') : t('app.chat.regenerateResponse') }}
    .message-box
      el-input(
        v-model="message"
        type="textarea"
        autosize
        :placeholder="t('app.chat.sendAMessage')"
        @keydown="handleKeyCode"
      )
      .submit
        i.iconfont.icon-send(@click="sendMsg")
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

// Props
interface Props {
  modelValue?: string
  showRegenerate?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  showRegenerate: false,
  loading: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  sendMsg: []
  regenerate: []
}>()

// Composables
const { t } = useI18n()

// Reactive state
const message = ref(props.modelValue)

// Methods
const handleKeyCode = (event: KeyboardEvent) => {
  if (event.keyCode === 13 && !event.shiftKey) {
    event.preventDefault()
    sendMsg()
  }
}

const sendMsg = () => {
  if (props.loading) {
    ElMessage.warning(t('app.chat.sendMsgFrequently'))
    return
  }
  if (message.value.trim()) {
    emit('sendMsg')
  }
}

const regenerate = () => {
  if (props.loading) {
    ElMessage.warning(t('app.chat.sendMsgFrequently'))
    return
  }
  emit('regenerate')
}

const setMsg = (val: string) => {
  message.value = val
}

// Watch for v-model sync
watch(
  () => message.value,
  (val) => {
    emit('update:modelValue', val)
  },
  { immediate: true }
)

// Watch for external prop changes
watch(
  () => props.modelValue,
  (val) => {
    message.value = val
  }
)

// Expose methods for parent component
defineExpose({
  setMsg
})
</script>

<style lang="scss" scoped>
@use 'index.scss';
</style>
