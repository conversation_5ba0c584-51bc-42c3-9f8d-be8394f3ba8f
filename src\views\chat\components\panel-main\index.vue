<template lang="pug">
  .chat-panel-main-wrapper(v-loading="loading")
    .chat-panel-main(v-if="currentChat")
      c-scroll.main(ref="scrollRef" :height="scrollHeight" :wrapStyle="'padding-right:0px'")
        .chat-detail(
          v-if="chatDetail"
          v-observe-el-height="chatHeightChangeEventName"
          :observe-immediate="true"
        )
          transition(name="el-zoom-in-top" mode="out-in")
            .loading-more(v-if="loadingMore && !pageConfig.isLastPage")
              i.el-icon-loading
          .item(v-for="(item, index) in chatDetail" :key="item.id")
            .question-box
              .item-top
                span.time {{ formatTime(item.createTime, timeFormat) }}
              .cont
                .avatar
                  img(:src="defaultAvatar")
                  .name {{ userInfo.userName || userInfo.sub }}
                .msg
                  .chat-msg-inhert-origin.is-question(v-html="item.requestText")
            .answer-box(
              v-if="item.allMessages && item.allMessages[item.answerIndex]"
              :class="`answer_${item.allMessages[item.answerIndex].id}`"
            )
              .item-top
                span.operate-item.time {{ formatTime(item.allMessages[item.answerIndex].createTime, timeFormat) }}
                i.operate-item.iconfont.icon-feedback(@click="feedback(item, index)")
                i.operate-item.iconfont.icon-ding(
                  :class="{'active': item.allMessages[item.answerIndex].flag === 1}"
                  @click="markerAnswer(1, item)"
                )
                i.operate-item.iconfont.icon-cai(
                  :class="{'active': item.allMessages[item.answerIndex].flag === 2}"
                  @click="markerAnswer(2, item)"
                )
              .cont
                .avatar
                  img(:src="logoImg")
                  .name {{ t('app.chat.fleetupAI') }}
                .msg
                  .chat-msg-inhert-origin.is-question(v-html="item.allMessages[item.answerIndex].responseText")
                  .chat-msg-type-cursor(
                    :class="{'chat-msg-type-cursor--blink': sendMsgLoading}"
                    v-if="item.allMessages[item.answerIndex].id === currentAnswerId && sendMsgLoading"
                  ) _
              .answer-paginator(v-if="item.allMessages && item.allMessages.length > 1")
                i.el-icon-arrow-left(@click="answerPageChange('prev', item)")
                .current-page {{ item.answerIndex + 1 }}
                .delimiter /
                .total {{ item.allMessages.length }}
                i.el-icon-arrow-right(@click="answerPageChange('next', item)")
          .spacing-box
      SendMessageBox(
        ref="sendMessageBoxRef"
        :loading="sendMsgLoading"
        :show-regenerate="showRegenerate"
        v-model="message"
        @sendMsg="sendMsg"
        @regenerate="regenerate"
      )
      transition(name="el-zoom-in-top" mode="out-in")
        .to-bottom(v-if="showBottom" @click="toBottom")
          i.iconfont.icon-bottom
    .empty-chat(v-else)
      img.logo(:src="logoImg")
      h1 {{ t('app.chat.fleetupAI') }}
      SendMessageBox(ref="sendMessageBoxRef" v-model="message" @sendMsg="emptySendMsg")
    DialogFeedback(ref="dialogFeedbackRef")
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCommonStore } from '@/stores/common'
import { useChatStore } from '@/stores/chat'
import { useEvents } from '@/composables/useEvents'
import CScroll from '@/components/c-scroll/index.vue'
import DialogFeedback from '@/views/chat/components/dialog-feedback/index.vue'
import SendMessageBox from '@/views/chat/components/send-message-box/index.vue'
import logoImg from '@/assets/images/common/logo.png'
import defaultAvatar from '@/assets/images/common/avatar.png'
import { useSendMessage } from '@/views/chat/composables/useSendMessage'
import { useChatDetail } from '@/views/chat/composables/useChatDetail'
import { uuid2 } from '@/utils'
import { getChatDetail } from '@/views/chat/api'
import moment from 'moment'

// Types
interface PageConfig {
  pageSize: number
  currentPage: number
  total: number
  order: string
  sort: string
  isLastPage: boolean
}

interface ChatMessage {
  id: string
  createTime: string
  requestText: string
  allMessages: Array<{
    id: string
    createTime: string
    responseText: string
    flag?: number
  }>
  answerIndex: number
}

// Composables
const { t } = useI18n()
const commonStore = useCommonStore()
const chatStore = useChatStore()
const { $on } = useEvents()

// Use mixins as composables
const sendMessageComposable = useSendMessage()
const chatDetailComposable = useChatDetail()

// Reactive state
const scrollHeight = ref('100%')
const loading = ref(false)
const chatDetail = ref<ChatMessage[] | null>(null)
const currentChatData = ref<any>(null)
const chatHeightChangeEventName = 'chatHeightChange'
const showBottom = ref(false)
const showRegenerate = ref(true)
const timeFormat = ref('YYYY-MM-DD')
const currentAnswerId = ref<string | null>(null)
const loadingMore = ref(false)
const firstScroll = ref(true)
const isInChangeChat = ref(false)
const firstBindScrollEvent = ref(false)
const oldChatId = ref<string | null>(null)
const cancelSourceList = ref<Record<string, any>>({})
const fetchAbortController = ref(new AbortController())
const inRegenerate = ref(false)
const currentMessage = ref<ChatMessage | null>(null)

const pageConfig = ref<PageConfig>({
  pageSize: 30,
  currentPage: 1,
  total: 0,
  order: 'createTime',
  sort: 'desc',
  isLastPage: false
})

// Refs
const scrollRef = ref<any>()
const sendMessageBoxRef = ref<any>()
const dialogFeedbackRef = ref<any>()

// Computed
const userInfo = computed(() => commonStore.userBaseInfo)
const currentChat = computed(() => chatStore.currentChat)
const chatListMap = computed(() => chatStore.chatListMap)
const chatData = computed(() => chatStore.chatData)

// Destructure composable methods and state
const {
  message,
  sendMsgLoading,
  sendMsg: sendMsgBase,
  regenerate: regenerateBase,
  saveMessageByCancelTheRequest,
  // ... other methods from useSendMessage
} = sendMessageComposable

const {
  // ... methods from useChatDetail
  markerAnswer,
  feedback,
  answerPageChange,
  // ... other methods
} = chatDetailComposable

// Time formatter
const formatTime = (time: string, format: string) => {
  if (!time) return ''
  const userTimezoneOffset = moment().utcOffset()
  const duration = moment.duration(Math.abs(userTimezoneOffset), 'minutes')
  const absTZ = moment.utc(duration.asMilliseconds()).format('HH:mm')
  const tzString = userTimezoneOffset > 0 ? `+${absTZ}` : `-${absTZ}`
  return moment.utc(time).local().format(`${format} HH:mm:ss`) + ` (UTC${tzString})`
}

// Methods
const clearRequest = async () => {
  const hasLegacyRequest = sendMsgLoading.value
  const isregenerate = inRegenerate.value
  let inSaveMessage = false

  Object.keys(cancelSourceList.value).forEach(key => {
    const item = cancelSourceList.value[key]
    item.cancel()
    inSaveMessage = true
    // if (key.startsWith('regenerate_')) isregenerate = true
    // hasLegacyRequest = true
  })

  cancelSourceList.value = {}
  fetchAbortController.value.abort()
  fetchAbortController.value = new AbortController()

  const currentMsg = currentMessage.value
  const answerRes: any = {}
  const lastAnswer = currentMsg && currentMsg.allMessages && currentMsg.allMessages[currentMsg.allMessages.length - 1]
  let emptyByRegenerate = false

  if (inSaveMessage) {
    // Abort a save message API, and then need to create a new answer id to avoid occur a conflict(409 error)
    currentAnswerId.value = uuid2()
    if (lastAnswer) {
      lastAnswer.id = currentAnswerId.value
    }
  }

  if (lastAnswer) {
    if (!lastAnswer.responseText) {
      // Clear empty answer
      currentMsg.allMessages.pop()
      currentMsg.answerIndex = currentMsg.allMessages.length - 1
      if (isregenerate) emptyByRegenerate = true
    } else {
      answerRes.responseText = lastAnswer.responseText
    }
  }

  if (currentMsg && hasLegacyRequest && !emptyByRegenerate && saveMessageByCancelTheRequest) {
    await saveMessageByCancelTheRequest({
      answerRes,
      chatDetail: chatDetail.value || [],
      message: currentMsg,
      answerId: currentAnswerId.value || '',
      chatId: oldChatId.value || '',
      isRegenerate: isregenerate
    })
  }

  if (!showRegenerate.value) showRegenerate.value = true
}

const selectChat = async (chatId: string) => {
  try {
    pageConfig.value = {
      pageSize: 30,
      currentPage: 1,
      total: 0,
      order: 'createTime',
      sort: 'desc',
      isLastPage: false
    }
    await clearRequest()
    chatDetail.value = []
    isInChangeChat.value = true
    loading.value = true

    currentAnswerId.value = null
    currentChatData.value = chatListMap.value[chatId]

    if (currentChatData.value) {
      await fetchMessageHistory({ currentPage: 1 })
      nextTick(() => {
        toBottom()
        if (firstScroll.value) {
          firstScroll.value = false
          nextTick(() => {
            toBottom()
            bindScrollEvent()
          })
        }
      })
    } else {
      showRegenerate.value = true
      pageConfig.value.total = 0
      pageConfig.value.isLastPage = true
    }
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
    isInChangeChat.value = false
  }
}

const bindScrollEvent = () => {
  if (!firstBindScrollEvent.value) {
    firstBindScrollEvent.value = true
    if (scrollRef.value) {
      const scrollEl = scrollRef.value.wrap
      scrollEl.addEventListener('scroll', handleScrollTopEvent)
    } else {
      nextTick(() => {
        const scrollEl = scrollRef.value.wrap
        scrollEl.addEventListener('scroll', handleScrollTopEvent)
      })
    }
  }
}

const handleScrollTopEvent = async () => {
  if (isInChangeChat.value || loadingMore.value || pageConfig.value.isLastPage || !scrollRef.value?.$el) return

  const chatBodyDom = scrollRef.value.wrap
  const currentScrollHeight = chatBodyDom.scrollHeight

  if (chatBodyDom.scrollTop === 0) {
    loadingMore.value = true
    try {
      await fetchMessageHistory({
        currentPage: pageConfig.value.currentPage + 1
      })
      const difference = chatBodyDom.scrollHeight - currentScrollHeight
      chatBodyDom.scrollTop += difference
    } finally {
      loadingMore.value = false
    }
  }
}

const fetchMessageHistory = async (opts: { currentPage: number }) => {
  try {
    const chatId = currentChat.value
    pageConfig.value = Object.assign(pageConfig.value, opts)
    const res = await getChatDetail(
      { id: chatId },
      {
        pageNum: pageConfig.value.currentPage,
        pageSize: pageConfig.value.pageSize,
        sort: pageConfig.value.sort,
        orderBy: pageConfig.value.order
      }
    )

    let list = res && res.list
    if (list && list.length) {
      list = formatData(list)
      pageConfig.value.total = res.total
      pageConfig.value.isLastPage = res.isLastPage
      // const res = this.getChatDetailFromStorage(chatId)
      // const formatData = this.formatData(res)
      if (!chatDetail.value) chatDetail.value = []
      chatDetail.value = list.concat(chatDetail.value)

      const length = chatDetail.value?.length || 0
      const lastMsg = length > 0 && chatDetail.value ? chatDetail.value[length - 1] : null
      showRegenerate.value = !!lastMsg
      // updateCurrentMessage()
      // chatStore.updateChatData({ id: this.currentChat, data: JSON.parse(JSON.stringify(formatData)) })
    } else {
      chatDetail.value = chatDetail.value || []
      showRegenerate.value = !!chatDetail.value.length
      pageConfig.value.total = chatDetail.value.length
      pageConfig.value.isLastPage = true
      // updateCurrentMessage()
    }
  } catch (e) {
    console.log(e)
  }
}

const formatData = (data: any[]) => {
  const res: ChatMessage[] = []
  for (let i = data.length - 1; i >= 0; i--) {
    const item = data[i]
    item.answerIndex = item.allMessages.length - 1
    res.push(item)
  }
  return res
}

const toBottom = () => {
  const distance = scrollRef.value.wrap.scrollHeight - scrollRef.value.wrap.scrollTop
  const step = distance > 0 ? ~~(distance / 10) : 500
  scrollRef.value && scrollRef.value.scrollToBottom(step)
}

const chatHeightChange = () => {
  showBottom.value = scrollRef.value.hasScroll()
}

const updateLocalStorage = (currentChatId?: string, chatDetailData?: any) => {
  chatStore.updateChatData({
    id: currentChatId || currentChat.value,
    data: chatDetailData || JSON.parse(JSON.stringify(chatDetail.value))
  })
}

// Update current message (usually the last message in chatDetail)
const updateCurrentMessage = () => {
  if (chatDetail.value && chatDetail.value.length > 0) {
    currentMessage.value = chatDetail.value[chatDetail.value.length - 1]
  } else {
    currentMessage.value = null
  }
}

// Wrapper methods for composables
const sendMsg = () => {
  inRegenerate.value = false
  sendMsgBase({}, {
    currentChat: currentChat.value,
    chatDetail: chatDetail.value || [],
    userInfo: userInfo.value,
    language: 'en', // TODO: Get from store
    timeFormat: timeFormat.value,
    fetchAbortController: fetchAbortController.value,
    cancelSourceList: cancelSourceList.value,
    scrollRef: scrollRef.value,
    sendMessageBoxRef: sendMessageBoxRef.value
  })
}

const regenerate = () => {
  inRegenerate.value = true
  regenerateBase({
    currentChat: currentChat.value,
    chatDetail: chatDetail.value || [],
    userInfo: userInfo.value,
    language: 'en', // TODO: Get from store
    timeFormat: timeFormat.value,
    fetchAbortController: fetchAbortController.value,
    cancelSourceList: cancelSourceList.value,
    scrollRef: scrollRef.value,
    sendMessageBoxRef: sendMessageBoxRef.value
  })
}

const emptySendMsg = () => {
  // TODO: Implement empty send message logic
  sendMsg()
}

// Lifecycle
onMounted(() => {
  cancelSourceList.value = {}
  fetchAbortController.value = new AbortController()

  // Use Pinia event system to listen for height changes (replacement for Vue 2's $on)
  $on(chatHeightChangeEventName, (payload) => {
    console.log(`[PanelMain] Chat height changed:`, payload)
    chatHeightChange()
  })
})

onUnmounted(() => {
  // Clean up scroll event listener from the scroll container (equivalent to Vue 2's destroyed hook)
  if (scrollRef.value?.wrap) {
    scrollRef.value.wrap.removeEventListener('scroll', handleScrollTopEvent)
  }
})

// Watchers
watch(currentChat, (val, old) => {
  if (val === null) return false
  oldChatId.value = old
  selectChat(val)
})

// Expose methods for parent component
defineExpose({
  selectChat,
  toBottom,
  updateLocalStorage,
  sendMsg,
  emptySendMsgLoading: ref(false), // TODO: Implement proper loading state
  deleteChat: (chatData: any) => {
    // TODO: Implement delete chat functionality
    console.log('Delete chat:', chatData)
  }
})
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>
