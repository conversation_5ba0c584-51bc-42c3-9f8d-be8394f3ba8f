<template lang="pug">
.wrap404
  h1 404
  .message {{ t('app.common.redirectMessage', { seconds: redirectTime }) }}
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const { t } = useI18n()
const redirectTime = ref(5)
let timer: any = null

onMounted(() => {
  timer = setInterval(() => {
    redirectTime.value--
    if (redirectTime.value === 0) {
      router.push('/')
    }
  }, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
