# Tests 测试目录

这个目录包含了项目的所有测试相关文件，包括自动化测试和手动测试组件。

## 📁 目录结构

```
tests/
├── README.md           # 本说明文档
├── unit/               # 单元测试
│   ├── components/     # 组件单元测试
│   ├── stores/         # 状态管理单元测试
│   └── example.spec.ts # 测试示例
├── e2e/                # 端到端测试
│   └── home.cy.ts      # 首页 E2E 测试
├── fixtures/           # 测试数据和模拟数据
│   └── articles.ts     # 文章测试数据
└── manual/             # 手动测试组件
    ├── README.md               # 手动测试说明
    ├── admin-info-test.vue     # 管理员信息测试
    ├── directives-test.vue     # Vue 指令测试
    └── c-scroll-test.vue       # 滚动组件测试
```

## 🧪 测试类型说明

### 1. Unit Tests (单元测试)
**目录：** `tests/unit/`
**工具：** Vitest
**用途：** 测试单个函数、组件或模块的功能

```bash
# 运行单元测试
npm run test:unit

# 监听模式运行单元测试
npm run test:unit:watch
```

**示例：**
```typescript
// tests/unit/components/CScroll.spec.ts
import { mount } from '@vue/test-utils'
import CScroll from '@/components/c-scroll/index.vue'

describe('CScroll Component', () => {
  it('should render correctly', () => {
    const wrapper = mount(CScroll, {
      props: { height: '300px' }
    })
    expect(wrapper.exists()).toBe(true)
  })
})
```

### 2. E2E Tests (端到端测试)
**目录：** `tests/e2e/`
**工具：** Cypress
**用途：** 测试完整的用户交互流程

```bash
# 运行 E2E 测试
npm run test:e2e

# 打开 Cypress 测试界面
npm run test:e2e:dev
```

**示例：**
```typescript
// tests/e2e/chat.cy.ts
describe('Chat Flow', () => {
  it('should send message successfully', () => {
    cy.visit('/chat')
    cy.get('[data-test="message-input"]').type('Hello')
    cy.get('[data-test="send-button"]').click()
    cy.contains('Hello').should('be.visible')
  })
})
```

### 3. Manual Tests (手动测试)
**目录：** `tests/manual/`
**用途：** 提供交互式界面，方便开发者手动验证功能

这些组件不会出现在生产环境中，仅用于开发和调试。

## 🚀 测试最佳实践

### 测试文件命名规范
- 单元测试：`ComponentName.spec.ts`
- E2E 测试：`feature-name.cy.ts`
- 手动测试：`feature-name-test.vue`

### 测试覆盖率
```bash
# 生成测试覆盖率报告
npm run test:coverage
```

### 测试数据管理
- 使用 `tests/fixtures/` 目录存放测试数据
- 创建可复用的模拟数据工厂函数
- 避免在测试中硬编码数据

### 组件测试策略
1. **浅层测试** - 测试组件的 props 和 emits
2. **交互测试** - 测试用户交互（点击、输入等）
3. **状态测试** - 测试组件状态变化
4. **集成测试** - 测试组件与 store 的交互

## 🔧 开发工作流

### 添加新功能时的测试流程
1. **编写单元测试** - 先写测试，后写实现（TDD）
2. **创建手动测试组件** - 用于开发期间的交互式测试
3. **编写 E2E 测试** - 覆盖关键用户流程
4. **运行所有测试** - 确保没有回归问题

### 调试测试
```bash
# 调试单元测试
npm run test:unit -- --reporter=verbose

# 调试 E2E 测试
npm run test:e2e:dev
```

## 📊 CI/CD 集成

### GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run unit tests
        run: npm run test:unit
      - name: Run E2E tests
        run: npm run test:e2e:headless
```

## 🛠️ 配置文件

### Vitest 配置
**文件：** `vitest.config.ts`
- 单元测试配置
- 覆盖率设置
- 模拟配置

### Cypress 配置
**文件：** `cypress.config.ts`
- E2E 测试配置
- 视口设置
- 插件配置

## 📚 相关文档

- [Vitest 官方文档](https://vitest.dev/)
- [Cypress 官方文档](https://docs.cypress.io/)
- [Vue Test Utils 文档](https://test-utils.vuejs.org/)
- [Testing Library 文档](https://testing-library.com/)

## ⚠️ 注意事项

1. **测试环境隔离** - 确保测试之间不相互影响
2. **异步测试** - 正确处理异步操作的测试
3. **模拟依赖** - 使用 mock 隔离外部依赖
4. **测试数据清理** - 每个测试后清理数据
5. **性能考虑** - 避免过慢的测试影响开发体验 