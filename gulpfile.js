'use strict'

// elementUI styles cli
import path from 'path'
import fs from 'fs'
import gulp from 'gulp'
import mergeJson from 'gulp-merge-json'
import plumber from 'gulp-plumber' // Prevent pipe breaking caused by errors from gulp plugins
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const pathResolve = (dir) => {
  return path.join(__dirname, './', dir)
}

const getFolders = (dir) => {
  return fs.readdirSync(dir).filter((file) => {
    try {
      return fs.lstatSync(path.join(dir, file)).isDirectory()
    } catch (e) {
      return false
    }
  })
}

const i18nTask = () => {
  // 调用getFolders方法获取到文件集合
  const folders = getFolders(pathResolve('pre-task/i18n/'))
  // 遍历得到每一个子文件
  const tasks = folders.map((folder) => {
    return gulp
      .src(pathResolve('pre-task/i18n/' + folder + '/*.json'))
      .pipe(plumber())
      .pipe(
        mergeJson({
          fileName: folder + '.json',
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          edit: (parsedJson, file) => {
            if (parsedJson.someValue) {
              delete parsedJson.otherValue
            }
            return parsedJson
          }
        })
      )
      .pipe(gulp.dest(pathResolve('public/data/i18n')))
  })
  
  // 返回合并后的任务流
  return Promise.all(tasks)
}

// 定义任务
gulp.task('i18n', i18nTask)

// 监视任务
const watchTask = () => {
  return gulp.watch(pathResolve('pre-task/i18n/**/*'), i18nTask)
}

// 默认任务
gulp.task('default', gulp.series('i18n', watchTask))

// 构建任务
gulp.task('build', gulp.series('i18n'))
