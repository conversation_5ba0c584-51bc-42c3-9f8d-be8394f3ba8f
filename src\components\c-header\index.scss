@use "@/assets/scss/imports.scss" as *;
@use "sass:color";

.bar-header {
  display: flex;
  height: 40px;
  box-sizing: border-box;
  margin: 0 auto;
  @include containerWidth();
  justify-content: space-between;
  align-items: center;

  .logo {
    display: flex;
    align-items: center;
    position: relative;

    .logo-holder {
      position: relative;
      background: rgba(255, 255, 255, 0.7);
      @include theme_bg_color("header-logo-background-color");
      height: 40px;

      &::after {
        content: "";
        width: 0;
        height: 0;
        position: absolute;
        z-index: 2;
        top: 0;
        left: 100%;
        border-right: none;
        border-top: 20px solid transparent;
        border-bottom: 20px solid transparent;
        border-left: 13px solid rgba(255, 255, 255, 0.7);
        @include border_color_left_header_arrow("header-logo-background-color");
      }

      img {
        height: 30px;
        margin: 5px 15px;
      }
    }

    p {
      height: 40px;
      line-height: 40px;
      background-color: #1F5783;
      @include theme_bg_color("header-logo-text-background-color");
      font-size: 18px;
      color: white;
      padding: 0 17px 0 30px;
      text-align: center;

      &::after {
        content: "";
        width: 0;
        height: 0;
        position: absolute;
        z-index: 2;
        top: 0;
        left: 100%;
        border-top: 20px solid transparent;
        border-bottom: 20px solid transparent;
        border-left: 13px solid #1f5783;
        @include border_color_left_header_arrow("header-logo-text-background-color");
      }
    }
  }

  .right {
    display: flex;
    color: white;
    font-size: 14px;
    align-items: center;

    .iconfont {
      font-size: 20px;
      color: white;
      cursor: pointer;

      &:hover {
        color: #FFA500;
      }
    }

    .item + .item {
      margin-left: 16px;

      html[lang=ar] & {
        margin-left: auto;
        margin-right: 16px;
      }
    }

    .theme-trigger {
      background: white;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      margin-right: 10px;
      background: rgba(0, 0, 0, .1) url("@/assets/images/common/theme-trigger.png") no-repeat center center;
      background-size: 20px 20px;
      box-sizing: border-box;

      &:hover {
        background-color: rgba(255, 255, 255, .1);
      }
    }


    .message-btn {
      position: relative;

      .count {
        box-sizing: border-box;
        background: #ff0000;
        cursor: default;
        border-radius: 15px;
        font-weight: bold;
        font-size: 12px;
        height: 16px;
        line-height: 16px;
        left: 13px;
        top: -4px;
        position: absolute;
        padding: 0 5px;
        text-align: center;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
        animation: pulse 1.5s 1;
      }
    }

    .driver-menu {
      height: 22px;
    }

    .icon-folder-open {
      font-size: 21px;
    }

    .icon-question-circle-copy {
      font-size: 21px;
    }

    .head-btn-api {
      padding: 3px 7px;
      background: #fff;
      color: #333;
      border-radius: 4px;
      border: 1px solid #ccc;
      text-align: center;
      font-size: 16px;
      line-height: 16px;
      cursor: pointer;

      &:hover {
        background: #FFA500;
        border-color: #FFA500;
      }

      &.video-link {
        &:hover {
          background-color: #e6e6e6;
          border-color: #e6e6e6;
        }
      }
    }

    .select-btn-group {
      display: flex;

      &.item {
        margin: 0 6px 0 7px;

        html[lang=ar] & {
          margin-right: 7px;
          margin-left: 6px;
        }
      }

      .caret {
        display: inline-block;
        width: 0;
        height: 0;
        margin-left: 4px;
        vertical-align: middle;
        border-top: 4px dashed;
        border-top: 4px solid;
        border-right: 4px solid transparent;
        border-left: 4px solid transparent;
      }

      :deep(.el-button--mini), .el-button--small {
        border-radius: 4px;
      }
    }

    .system-language {

      :deep(.el-button--small) {
        border-top-right-radius: unset;
        border-bottom-right-radius: unset;
        border-right: none;
        color: #333;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.8;
      }

      .current-lang {
        color: #fff;

        .text {
          margin: 0 5px;
        }
      }
    }

    .system-action {
      margin: 0 7px 0 5px;

      :deep(.el-button--small) {
        border-top-left-radius: unset;
        border-bottom-left-radius: unset;
        text-align: right;
        line-height: 1.8;
        color: #333;
        font-size: 14px;
      }

      .name {
        color: #fff;

        .text {
          margin: 0 5px;
        }
      }
    }

    .ai-badge {
      margin-right: 12px;

      html[lang=es] & {
        margin-right: 20px;
      }

      html[lang=po] & {
        margin-right: 16px;
      }

      :deep(.el-badge__content ) {
        top: 3px;
        background-color: $color-badge-new;
        border: none;
        transform: translateY(-50%) translateX(90%) scale(0.75);
      }
    }

    .icon-ai {
      cursor: pointer;

      &:disabled {
        color: #ccc;
        cursor: not-allowed;
      }
    }

    .slogan {
      display: flex;
      align-items: center;

      .logo {
        height: 30px;
        object-fit: contain;
      }

      .txt {
        margin: 0 5px;
      }
    }
  }
}

.link-dropdown-menu {
  overflow: unset;

  a {
    display: inline-block;
    width: 100%;
  }

  .videos-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 -20px;
    padding: 0 20px;
  }

  .videos-list {
    position: absolute;
    top: 100%;
    left: 100%;
    padding: 5px 0;
    margin: -40px 0 0;
    background: white;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    width: fit-content;

    li {
      list-style: none;
      line-height: 20px;
      padding: 5px 20px;
      margin: 2px 0 0;
      font-size: 14px;
      color: #606266;
      cursor: pointer;
      outline: 0;

      &:hover {
        background: #E6EAEF;
      }
    }
  }
}

.dropdown-list {
  margin-top: 2px;
  padding: 5px 0;

  :deep(.el-dropdown-menu__item) {
    line-height: 1.4;
    padding: 3px 13px;
  }

  .action-link {
    color: #606266;
    display: block;
    width: 124px;
  }

  .selected {
    background: #337ab7;
    color: white;
  }

  .language-item {
    display: block;
    width: 132px;
  }
}

html[lang=ar] {
  .bar-header {
    .logo {
      .logo-holder {
        &::after {
          left: unset;
          right: 100%;
          border-left: unset;
          border-right: 13px solid rgba(255, 255, 255, 0.7);
        }
      }

      p {
        padding: 0 30px 0 17px;

        &::after {
          left: 0;
          right: 100%;
          border-left: unset;
          border-right: 13px solid #1f5783;
        }
      }
    }

    .right {
      .head-btn-api {
        //margin-left: 5px;
        //margin-right: 10px;
      }

      .system-language {
        //margin-left: 0;
        //margin-right: 5px;

        :deep(.el-button--small) {
          border-top-right-radius: 3px;
          border-bottom-right-radius: 3px;
          border-top-left-radius: unset;
          border-bottom-left-radius: unset;
          border-right: 1px solid #DCDFE6;
          border-left: none;
        }
      }

      .system-action {
        //margin-right: 0;
        //margin-left: 7px;

        :deep(.el-button--small) {
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
          border-top-right-radius: unset;
          border-bottom-right-radius: unset;
          text-align: left;
        }
      }
    }
  }
}

