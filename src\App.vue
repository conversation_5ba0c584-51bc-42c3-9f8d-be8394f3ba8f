<template lang="pug">
  #wrap
    keep-alive
      router-view(v-if="route.meta.keepAlive")
    router-view(v-if="!route.meta.keepAlive", :key="key")
</template>

<script setup lang="ts">
// App component
import { useLanguageStore } from '@/stores/language'
import { useCommonStore } from '@/stores/common'
import { useRoute } from 'vue-router'
import { computed, onMounted } from 'vue'

const languageStore = useLanguageStore()
const commonStore = useCommonStore()
const route = useRoute()

// Initialize language package
languageStore.initLangPackage()

// Computed properties
const key = computed(() => {
  return (route.name?.toString() || 'default') + +new Date()
})

// Application-level initialization
onMounted(async () => {
  // Initialize administrator information (application-level, only once per session)
  try {
    await commonStore.initAdminInfo()
    console.log('App: Admin info initialization completed successfully')
  } catch (error) {
    // Error handling is already done in the store, but we can add app-level error handling here if needed
    console.log('App: Admin info initialization completed with error')
  }

  // You can add other application-level initialization logic here
  // For example: theme initialization, global settings, etc.
})
</script>

<style lang="scss">
@use "@/assets/scss/app.scss";
</style>
