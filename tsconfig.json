// tsconfig.json
{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "bundler", // Recommended for Vite - bundler
    "composite": true, // Support build cache
    "esModuleInterop": true, // Support for default imports from CommonJS modules
    "allowSyntheticDefaultImports": true, // Allow synthetic default imports
    "resolveJsonModule": true, // 允许导入 JSON 文件
    "isolatedModules": true, // 确保每个文件都可以独立编译
    "allowImportingTsExtensions": true, // 允许导入 .ts 扩展名
    "noEmit": true, // 不生成输出文件
    "strict": true, // 启用严格模式
    "skipLibCheck": true, // 跳过库文件的类型检查
    "forceConsistentCasingInFileNames": true, // 强制文件名大小写一致
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@res/*": ["./public/res/*"],
      "@data/*": ["./public/data/*"]
    },
    "types": ["vite/client", "element-plus/global", "vue", "node"]
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "src/**/*.ts",
    "src/**/*.tsx",
    "vite.config.*",
    "plugins/**/*.ts",
    "auto-imports.d.ts",
    "components.d.ts"
  ],
  "exclude": ["src/**/__tests__/*", "node_modules", "dist", "cypress"]
}
