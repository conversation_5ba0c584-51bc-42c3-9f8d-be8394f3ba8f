<template lang="pug">
  .page-chat
    panelLeft(ref="panelLeftRef" @selectChat="handlerSelectChat" @deleteChat="handlerDeleteChat")
    panelMain(ref="panelMainRef" @createChat="handleCreateChat")
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useCommonStore } from '@/stores/common'
import panelLeft from '@/views/chat/components/panel-left/index.vue'
import panelMain from '@/views/chat/components/panel-main/index.vue'

// Store
const commonStore = useCommonStore()

// Refs
const panelLeftRef = ref<InstanceType<typeof panelLeft>>()
const panelMainRef = ref<InstanceType<typeof panelMain>>()

// Computed
const userInfo = computed(() => commonStore.userBaseInfo)

// Methods
const handlerSelectChat = (chatData: any) => {
  // panelMainRef.value?.selectChat(chatData)
}

const handlerDeleteChat = (chatData: any) => {
  panelMainRef.value?.deleteChat(chatData)
}

const handleCreateChat = async () => {
  try {
    const newRes = await panelLeftRef.value?.newChat()
    if (!newRes) return false
    
    nextTick(async () => {
      try {
        await panelMainRef.value?.sendMsg()
      } finally {
        if (panelMainRef.value) {
          panelMainRef.value.emptySendMsgLoading = false
        }
      }
    })
  } catch (e) {
    console.error(e)
    if (panelMainRef.value) {
      panelMainRef.value.emptySendMsgLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
