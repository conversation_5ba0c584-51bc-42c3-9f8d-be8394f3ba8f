import moment from 'moment'

const LEAP_MONTHS = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
const NONLEAP_MONTHS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

interface ProcessingModule {
  daysInYear(year: number): number
  daysInMonth(year: number, month: number): number
  isLeapYear(year: number): boolean
  takesTime(val: string | Date, other: string | Date, format?: string): string
  seconds2HMS(diff: number): [number, number, number]
  secondsFormat(diff: number, format?: string): string
  milliseconds2HMS(diff: number): [number, number, number]
  toZeroStr(val: number, num?: number): string
  getBirthday(psidno: string): string[] | null
  getSex(psidno: string): number | null
  setTitle(title: string): void
}

const self: ProcessingModule = {
  daysInYear(year: number): number {
    return self.isLeapYear(year) ? 366 : 365
  },
  
  daysInMonth(year: number, month: number): number {
    return self.isLeapYear(year) ? LEAP_MONTHS[month] : NONLEAP_MONTHS[month]
  },
  
  isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
  },
  
  takesTime(val: string | Date, other: string | Date, format?: string): string {
    if (!val) {
      return String(val)
    }
    const mbegin = moment(val)
    const mend = moment(other)
    const diff = mend.diff(mbegin, 'seconds')
    return self.secondsFormat(diff, format)
  },
  
  seconds2HMS(diff: number): [number, number, number] {
    const seconds = diff % 60
    const minutes = (diff - seconds) % 3600
    const hours = diff - minutes - seconds
    return [hours / 3600, minutes / 60, seconds]
  },
  
  secondsFormat(diff: number, format?: string): string {
    const formatStr = format || 'HH小时mm分'
    const val = self.seconds2HMS(diff)
    return formatStr
      .replace(/HH/g, String(val[0]))
      .replace(/mm/g, String(val[1]))
      .replace(/ss/g, String(val[2]))
  },
  
  milliseconds2HMS(diff: number): [number, number, number] {
    const millisecond = diff % 1000
    const adjustedDiff = diff - millisecond
    return self.seconds2HMS(adjustedDiff / 1000)
  },
  
  toZeroStr(val: number, num: number = 2): string {
    return (new Array(num).join('0') + val).slice(-num)
  },
  
  // 获取生日
  getBirthday(psidno: string): string[] | null {
    let birthdayno: string
    let birthdaytemp: string
    
    if (psidno.length === 18) {
      birthdayno = psidno.substring(6, 14)
    } else if (psidno.length === 15) {
      birthdaytemp = psidno.substring(6, 12)
      birthdayno = `19${birthdaytemp}`
    } else {
      return null
    }
    
    const birthday = [
      birthdayno.substring(0, 4),
      birthdayno.substring(4, 6),
      birthdayno.substring(6, 8)
    ]
    
    return moment(birthday).isValid() ? birthday : null
  },
  
  // 获取性别
  getSex(psidno: string): number | null {
    let sexno: string
    
    if (psidno.length === 18) {
      sexno = psidno.substring(16, 17)
    } else if (psidno.length === 15) {
      sexno = psidno.substring(14, 15)
    } else {
      return null
    }
    
    const tempid = parseInt(sexno) % 2
    return tempid === 0 ? 0 : 1
  },
  
  // 设置标题
  setTitle(title: string): void {
    document.title = title
    const mobile = navigator.userAgent.toLowerCase()
    
    if (/iphone|ipad|ipod/.test(mobile)) {
      const iframe = document.createElement('iframe')
      iframe.style.visibility = 'hidden'
      iframe.style.width = '1px'
      iframe.style.height = '1px'
      iframe.setAttribute('src', '/favicon.ico')
      
      const iframeCallback = () => {
        setTimeout(() => {
          iframe.removeEventListener('load', iframeCallback)
          document.body.removeChild(iframe)
        }, 0)
      }
      
      iframe.addEventListener('load', iframeCallback)
      document.body.appendChild(iframe)
    }
  }
}

export default self 