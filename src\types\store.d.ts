declare module 'store' {
  interface Storage {
    get(key: string): any;
    set(key: string, value: any, expiration?: number): void;
    remove(key: string): void;
    clearAll(): void;
    each(callback: (value: any, key: string) => void): void;
    addPlugin(plugin: any): void;
  }
  
  const storage: Storage;
  export default storage;
}

declare module 'store/plugins/expire' {
  const expirePlugin: any;
  export default expirePlugin;
} 