import type { Directive, DirectiveBinding } from 'vue'

// Since the original image may not exist, we use a generic placeholder or inline SVG
const comingSoonContent = `
  <div class="coming-soon-black" style="display: flex; align-items: center; justify-content: center; padding: 20px; background-color: #f5f5f5; border-radius: 4px; color: #666;">
    <span style="font-size: 14px;">Coming Soon</span>
  </div>
`

function updateComingSoon(el: HTMLElement, binding: DirectiveBinding) {
  el.innerHTML = comingSoonContent
}

const comingSoon: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    updateComingSoon(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    updateComingSoon(el, binding)
  }
}

export default {
  'coming-soon': comingSoon
} 