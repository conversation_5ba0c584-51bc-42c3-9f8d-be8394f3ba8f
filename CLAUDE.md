# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Development
- `npm run dev` - Start development server on port 8916
- `npm run build` - Build production version (includes type checking)
- `npm run preview` - Preview production build
- `npm run type-check` - Run TypeScript type checking
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

### Testing
- `npm run test:unit` - Run unit tests with Vitest
- `npm run test:unit:coverage` - Run unit tests with coverage report
- `npm run test:e2e` - Run E2E tests with Cypress
- `npm run test:e2e:dev` - Run E2E tests in development mode

### Utilities
- `npm run generate-sitemap` - Generate sitemap using scripts/generate-sitemap.ts

## Architecture Overview

### Core Framework
- **Vue 3** with Composition API (`<script setup>` syntax)
- **TypeScript** for type safety
- **Vite** as build tool and dev server
- **Pinia** for state management
- **Vue Router** for routing

### UI & Styling
- **Element Plus** as primary UI component library
- **TailwindCSS** for utility-first styling
- **SCSS** for custom styles (each component has `index.vue` + `index.scss` structure)
- **Pug** templates are required for all Vue components

### Key Libraries
- **vue-i18n** for internationalization
- **GSAP** and **AOS** for animations  
- **FontAwesome** for icons
- **Axios** for HTTP requests
- **@vueuse/head** for SEO management

### Project Structure
- `src/components/` - Reusable UI components (each in folder with `index.vue` + `index.scss`)
- `src/views/` - Page components
- `src/stores/` - Pinia stores for state management
- `src/composables/` - Vue composables for shared logic
- `src/directives/` - Custom Vue directives
- `src/utils/` - Utility functions
- `src/i18n/langs/` - Language translation files
- `src/assets/scss/` - Global SCSS styles

### API Integration
Development proxy configuration:
- `/api` → `https://chatbot.dev.fleetup.net/`
- `/service_api` → `https://chatbotservice.ai.fleetup.cc/`
- `/message_api` → `http://52.25.26.156/chatservice/`

### Authentication
- Uses `storage.xAuthToken` for authentication
- Routes are protected unless `meta.ignoreAuth` is set
- Login redirects to `/login` route

### Key Features
- **FleetUp AI Search and Analytics** - Main AI-powered search interface
- **Multi-language support** - AR, EN, ES, PT with dynamic switching
- **Theme system** - Light/dark mode support
- **Responsive design** - Mobile-first approach
- **SEO optimization** - Meta tags, sitemap generation, structured data

### Development Guidelines
- Use Vue 3 Composition API with `<script setup>`
- All templates must use Pug language
- Follow component structure: folder with `index.vue` + `index.scss`
- Use TypeScript interfaces, avoid enums
- Prefer named exports
- Use ESLint and Prettier for code formatting
- Write components using Element Plus design system

### Testing
- **Vitest** for unit testing with jsdom environment
- **Cypress** for E2E testing
- Test files located in `tests/` directory
- Manual testing components in `tests/manual/`