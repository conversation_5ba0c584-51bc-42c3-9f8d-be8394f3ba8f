<template lang="pug">
el-dialog.dialog-feedback(
  v-model="visible"
  center
  top="3vh"
  append-to-body
  @close="close"
)
  template(#header)
    .title
      | {{ t('app.chat.feedbackDialogTitle') }} &nbsp;&nbsp;
      el-popover(
        placement="bottom"
        title=""
        width="400"
        trigger="hover"
        :content="t('app.chat.feedbackDialogTitleTip')"
      )
        template(#reference)
          i.el-icon-info
  .dialog-content(v-loading="loading")
    el-form.form(
      ref="formRef"
      label-position="top"
      :validate-on-rule-change="false"
      :rules="rules"
      :model="form"
      @submit.prevent
    )
      el-form-item(prop="Message")
        .form-display-item
          .icon
            img(:src="userAvatar")
          .text-content.chat-msg-inhert-origin.is-question(v-html="form.message")
        .form-display-item
          .icon
            img(:src="aiAvatar")
          .text-content.chat-msg-inhert-origin.is-question(v-html="form.response")
      el-form-item(prop="feedback")
        el-alert(
          :title="t('app.chat.feedbackTip')"
          type="info"
          show-icon
          :closable="false"
        )
        el-input(
          type="textarea"
          v-model="form.feedback"
          auto-complete="on"
          name="feedback"
          :placeholder="t('app.chat.feedbackPlaceholder')"
        )
      el-form-item(prop="correctAnswer")
        el-alert(
          :title="t('app.chat.correctAnswerTip')"
          type="info"
          show-icon
          :closable="false"
        )
        el-input(
          type="textarea"
          v-model="form.correctAnswer"
          auto-complete="on"
          name="correctAnswer"
        )
  template(#footer)
    .footer
      el-button(type="primary" @click="handleSend") {{ t('app.ui.send') }}
      el-button(@click="visible = false") {{ t('app.ui.cancel') }}
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useChatStore } from '@/stores/chat'
import logoImg from '@/assets/images/common/logo.png'
import defaultAvatar from '@/assets/images/common/avatar.png'
import { createFormRules } from './form-rules'
import { feedback } from '@/views/chat/api'

// Types
interface ChatData {
    allMessages: Array<{
        id: string
        responseText: string
    }>
    answerIndex: number
    requestText: string
}

interface FormData {
    message: string
    response: string
    feedback: string
    correctAnswer: string
}

// Composables
const { t } = useI18n()
const chatStore = useChatStore()

// Reactive state
const aiAvatar = logoImg
const userAvatar = defaultAvatar
const visible = ref(false)
const loading = ref(false)
const chatData = ref<ChatData | null>(null)
const question = ref<ChatData | null>(null)
const answer = ref<any>(null)

const form = ref<FormData>({
    message: '',
    response: '',
    feedback: '',
    correctAnswer: ''
})

// Refs
const formRef = ref<FormInstance>()

// Computed
const currentChat = computed(() => chatStore.currentChat)
const rules = computed(() => createFormRules(t))

// Methods
const show = (data: ChatData) => {
    visible.value = true
    answer.value = data.allMessages[data.answerIndex]
    question.value = data
    form.value.response = answer.value.responseText
    form.value.message = question.value.requestText
    form.value.feedback = ''
    form.value.correctAnswer = ''
    formRef.value?.clearValidate()
}

const close = () => {
    formRef.value?.resetFields()
    formRef.value?.clearValidate()
}

const handleSend = async () => {
    try {
        loading.value = true
        const valid = await formRef.value?.validate()
        if (valid) {
            // api
            const params = {
                reason: form.value.feedback,
                expectedAnswer: form.value.correctAnswer
            }
            await feedback(params, { id: currentChat.value, mid: answer.value.id })
            visible.value = false
            ElMessage.success(t('app.ui.submittedSuccessfully'))
        }
    } catch (e) {
        console.log(e)
    } finally {
        loading.value = false
    }
}

// Expose methods for parent component
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
@use './index.scss';
</style>