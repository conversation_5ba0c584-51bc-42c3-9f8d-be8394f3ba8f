# clearRequest 方法迁移文档

## 概述

`clearRequest` 方法是聊天组件中的一个重要方法，用于取消正在进行的请求并处理相关的状态清理。

## Vue 2 vs Vue 3 对比

### Vue 2 原始实现

```javascript
async clearRequest () {
  let hasLegacyRequest = this.sendMsgLoading, isregenerate = this.inRegenerate, inSaveMessage
  Object.keys(this.cancelSourceList).forEach(key => {
    const item = this.cancelSourceList[key]
    item.cancel()
    inSaveMessage = true
  })

  this.cancelSourceList = {}
  this.fetchAbortController.abort()
  this.fetchAbortController = new AbortController()
  
  const currentMessage = this.currentMessage, answerRes = {}
  const lastAnswer = currentMessage && currentMessage.allMessages && currentMessage.allMessages[currentMessage.allMessages.length - 1]
  let emptyByRegenerate = false
  
  if (inSaveMessage) {
    this.currentAnswerId = uuid2()
    lastAnswer.id = this.currentAnswerId
  }
  
  if (lastAnswer) {
    if (!lastAnswer.responseText) {
      currentMessage.allMessages.pop()
      currentMessage.answerIndex = currentMessage.allMessages.length - 1
      if (isregenerate) emptyByRegenerate = true
    } else {
      answerRes.responseText = lastAnswer.responseText
    }
  }
  
  currentMessage && hasLegacyRequest && !emptyByRegenerate && await this.saveMessageByCancelTheRequest({
    answerRes, 
    chatDetail: this.chatDetail, 
    message: currentMessage, 
    answerId: this.currentAnswerId, 
    chatId: this.oldChatId, 
    isRegenerate: isregenerate 
  })
  
  if (!this.showRegenerate) this.showRegenerate = true
}
```

### Vue 3 迁移后实现

```typescript
const clearRequest = async () => {
  const hasLegacyRequest = sendMsgLoading.value
  const isregenerate = inRegenerate.value
  let inSaveMessage = false

  Object.keys(cancelSourceList.value).forEach(key => {
    const item = cancelSourceList.value[key]
    item.cancel()
    inSaveMessage = true
  })

  cancelSourceList.value = {}
  fetchAbortController.value.abort()
  fetchAbortController.value = new AbortController()

  const currentMsg = currentMessage.value
  const answerRes: any = {}
  const lastAnswer = currentMsg && currentMsg.allMessages && currentMsg.allMessages[currentMsg.allMessages.length - 1]
  let emptyByRegenerate = false

  if (inSaveMessage) {
    // Abort a save message API, and then need to create a new answer id to avoid occur a conflict(409 error)
    currentAnswerId.value = uuid2()
    if (lastAnswer) {
      lastAnswer.id = currentAnswerId.value
    }
  }

  if (lastAnswer) {
    if (!lastAnswer.responseText) {
      // Clear empty answer
      currentMsg.allMessages.pop()
      currentMsg.answerIndex = currentMsg.allMessages.length - 1
      if (isregenerate) emptyByRegenerate = true
    } else {
      answerRes.responseText = lastAnswer.responseText
    }
  }

  if (currentMsg && hasLegacyRequest && !emptyByRegenerate && saveMessageByCancelTheRequest) {
    await saveMessageByCancelTheRequest({
      answerRes,
      chatDetail: chatDetail.value,
      message: currentMsg,
      answerId: currentAnswerId.value,
      chatId: oldChatId.value,
      isRegenerate: isregenerate
    })
  }

  if (!showRegenerate.value) showRegenerate.value = true
}
```

## 主要变化

### 1. 响应式数据访问

| Vue 2 | Vue 3 |
|-------|-------|
| `this.sendMsgLoading` | `sendMsgLoading.value` |
| `this.inRegenerate` | `inRegenerate.value` |
| `this.cancelSourceList` | `cancelSourceList.value` |
| `this.currentMessage` | `currentMessage.value` |

### 2. 新增状态管理

```typescript
// 新增的 ref 状态
const inRegenerate = ref(false)
const currentMessage = ref<ChatMessage | null>(null)

// 辅助函数
const updateCurrentMessage = () => {
  if (chatDetail.value && chatDetail.value.length > 0) {
    currentMessage.value = chatDetail.value[chatDetail.value.length - 1]
  } else {
    currentMessage.value = null
  }
}
```

### 3. 状态更新逻辑

```typescript
// 在发送消息时重置再生成状态
const sendMsg = () => {
  inRegenerate.value = false
  // ... 其他逻辑
}

// 在再生成时设置状态
const regenerate = () => {
  inRegenerate.value = true
  // ... 其他逻辑
}

// 在获取消息历史后更新当前消息
await fetchMessageHistory({ currentPage: 1 })
updateCurrentMessage()
```

### 4. Composable 集成

```typescript
// 从 composable 中获取保存方法
const {
  message,
  sendMsgLoading,
  sendMsg: sendMsgBase,
  regenerate: regenerateBase,
  saveMessageByCancelTheRequest, // ← 新增
} = sendMessageComposable
```

## 关键改进

### ✅ 类型安全
- 所有状态都有明确的 TypeScript 类型
- `ChatMessage` 接口定义了消息结构

### ✅ 响应式系统
- 使用 Vue 3 的 `ref` 和 `computed` 
- 自动追踪依赖关系

### ✅ 组合式 API
- 逻辑更清晰，易于测试
- 方法从 composable 中导入

### ✅ 错误处理
- 更好的空值检查
- 安全的属性访问

## 使用场景

### 取消请求时机
1. **用户主动取消**: 点击取消按钮
2. **切换聊天**: 选择其他聊天会话
3. **组件卸载**: 页面关闭或导航离开
4. **重新生成**: 用户点击重新生成按钮

### 状态清理内容
1. **网络请求**: 取消所有进行中的 HTTP 请求
2. **定时器**: 清理相关的 setTimeout/setInterval
3. **消息状态**: 处理未完成的消息内容
4. **UI 状态**: 重置加载状态和按钮状态

## 注意事项

1. **内存泄漏防护**: 确保所有请求都被正确取消
2. **状态一致性**: 保持 UI 状态与数据状态同步
3. **用户体验**: 取消操作应该是即时的
4. **数据完整性**: 避免部分数据丢失

这个迁移保持了原有功能的完整性，同时利用了 Vue 3 的新特性提供更好的性能和开发体验。 