.c-footer {
  background-color: var(--primary-secondary-color);
  color: white;
  padding: 4rem 0;
  width: 100%;
  overflow: hidden;

  .container {
    width: 100%;
    padding: 0 2rem;
  }

  .footer-content {
    display: grid;
    grid-template-columns: minmax(200px, 1.5fr) minmax(300px, 2fr) minmax(150px, 1fr);
    gap: 2rem;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr 1fr;
      gap: 2rem;

      .social-links {
        grid-column: span 2;
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 2rem;

      .social-links {
        grid-column: auto;
      }
    }
  }

  .footer-info {
    .company-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;

      .company-logo {
        width: 48px;
        height: 48px;
        object-fit: contain;
      }

      .company-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: white;
      }
    }

    .copyright {
      font-size: 0.875rem;
      opacity: 0.8;
      line-height: 1.5;
    }
  }

  .footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .link-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 0;
    align-items: flex-start;

    .link-mul {
      display: flex;
    }

    .link-item {
      display: flex;
      flex-flow: column;

      & + .link-item {
        margin-left: 1.5rem;
      }
    }
  }

  .link-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: white;
  }

  .footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: normal;
    word-break: break-word;

    &:hover {
      color: white;
    }
  }

  .footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: normal;
    word-break: break-word;
  }

  .social-links {
    .social-icons {
      display: flex;
      gap: 1.5rem;
      margin-top: 1rem;
      flex-wrap: wrap;
    }

    .social-link {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1.5rem;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      text-decoration: none;

      &:hover {
        color: white;
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .iconfont {
        font-size: 1.4rem;
      }
    }
  }
}
