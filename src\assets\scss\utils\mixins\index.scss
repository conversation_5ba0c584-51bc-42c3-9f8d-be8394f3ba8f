@use "@/assets/scss/utils/constants" as *;
@use "@/assets/scss/themes/_themes" as *;
@use "./x-mixins";

@mixin containerWidth() {
  width: $page-width;
}


@mixin ellipsis() {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}


@mixin img-gray() {
  -webkit-filter: grayscale(100%); /* Chrome, Safari, Opera */
  filter: grayscale(100%);
}


@mixin transition($arguments...) {
  $param: unquote(join (",", $arguments));
  -webkit-transition: $param;
  -moz-transition: $param;
  -o-transition: $param;
  -ms-transition: $param;
  transition: $param;
}


@mixin z-box-shadow($arguments...) {
  $param: unquote(join (",", $arguments));
  -moz-box-shadow: $param;
  -webkit-box-shadow: $param;
  box-shadow: $param;
}


@mixin input-shadow($border_color: rgba(82, 168, 236, 0.8), $box_color: rgba(82, 168, 236, 0.6)) {
  @if $box_color == none {

    @include transition(none, none);
    &:focus {
      border-color: $border_color;
      outline: none;

      @include z-box-shadow(none, none);
    }
  } @else {

    @include transition(border linear 0.3s, box-shadow linear 0.3s);
    &:focus {
      border-color: $border_color;
      outline: thin dotted;
      /* IE6-9 */

      @include z-box-shadow(inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px $box_color);
    }
  }
}

@mixin btn-light() {
  background-color: $color-theme-brighten;
  border-color: $color-theme-brighten;
  color: #fff;
}

@mixin fu-scroll-bar {
  $c-scrollbar-thumb-background: #b4bccc;
  $c-scrollbar-track-background: #fff;

  &::-webkit-scrollbar {
    z-index: 11;
    width: 6px;

    &:horizontal {
      height: 6px;
    }

    &-thumb {
      border-radius: 5px;
      width: 6px;
      background: $c-scrollbar-thumb-background;
    }

    &-corner {
      background: $c-scrollbar-track-background;
    }

    &-track {
      background: $c-scrollbar-track-background;

      &-piece {
        background: $c-scrollbar-track-background;
        width: 6px;
      }
    }
  }
}
