@use "@/assets/scss/utils/constants" as *;
@use "@/assets/scss/utils/mixins/index" as *;
.send-wrapper {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px 15px;
  background-image: linear-gradient(
    180deg,
    rgba(241, 245, 247, 0) 13.94%,
    $SP-layout-body-bgcolor 54.73%
  );
  .regenerate,
  .message-box {
    display: flex;
    justify-content: center;
  }
  .regenerate-btn,
  .message-box {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  }
  .regenerate {
    margin-bottom: 10px;
    .icon-refresh {
      margin-right: 8px;
    }
  }
  .message-box {
    position: relative;
    border-radius: 15px;
    .submit {
      position: absolute;
      right: 15px;
      height: 3.5em;
      line-height: 3.5em;
      overflow: hidden;

      .iconfont {
        font-size: 25px;
        color: #acde80;
        transition: all 0.3s;
        cursor: pointer;
        &:hover {
          font-size: 34px;
          color: $color-theme;
        }
      }
    }
    :deep(textarea) {
      padding: 15px;
      font-family: inherit;
      resize: none;
      line-height: 1.5em;
      transition: all 0.3s;
      &:hover {
        border-color: $color-theme;
        box-shadow: 1px 3px 3px rgba(0, 46, 93,0.15);
      }
    }
  }
}
