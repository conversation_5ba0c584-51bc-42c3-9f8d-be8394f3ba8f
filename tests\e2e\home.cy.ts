// Test home page
describe('Home Page', () => {
  beforeEach(() => {
    // 访问首页
    cy.visit('/')
  })

  it('should display correctly and navigate to articles', () => {
    // Check if page contains expected text
    cy.contains('h1', 'Welcome to FleetUp')
    cy.contains('p', 'Your trusted partner in aviation')

    // Click browse articles button
    cy.get('[data-test="browse-articles"]').click()

    // Check if URL has changed
    cy.url().should('include', '/articles')

    // Check article page title
    cy.get('h1').should('contain', 'Articles')
  })

  it('should display featured articles', () => {
    // Check if featured articles section exists
    cy.get('[data-test="featured-articles"]').should('exist')

    // Wait for articles to load
    cy.get('[data-test="article-card"]').should('have.length.gt', 0)
  })
}) 