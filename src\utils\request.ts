import axios from 'axios'
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
  CancelTokenSource
} from 'axios'
import jsCookie from 'js-cookie'
import { ElLoading, ElMessage } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'
import storage from '@/commons/storage'

// API configuration
interface ApiConfig {
  DEFAULT: string
  TRUNK: string
  SZDEV: string
  [key: string]: string
}

const API_CONFIG: {
  development: ApiConfig
  production: ApiConfig
} = {
  development: {
    LOCAL: import.meta.env.BASE_URL,
    DEFAULT: '/api',
    TRUNK: '/trunk',
    SZDEV: '/szdev'
  },
  production: {
    LOCAL: import.meta.env.BASE_URL,
    DEFAULT: '/api',
    TRUNK: '/trunk',
    SZDEV: '/szdev'
  }
}

// environment variable
const isProd = import.meta.env.PROD
const BASE_PATH = isProd ? API_CONFIG.production : API_CONFIG.development

// HTTP status information interface
interface HttpStatus {
  [key: number]: string
  DEFAULT_ERROR: string
  [key: string]: string
}

// Note: here we use the status information in storage first, if it does not exist, we use the default value
const HTTP_STATUS: HttpStatus = Object.assign(
  {},
  {
    400: 'request error',
    401: 'unauthorized, please login again',
    403: 'access denied',
    404: 'request address error',
    408: 'request timeout',
    500: 'server internal error',
    501: 'service not implemented',
    502: 'gateway error',
    503: 'service unavailable',
    504: 'gateway timeout',
    505: 'HTTP version not supported',
    DEFAULT_ERROR: 'system exception, please try again later'
  },
  storage.httpStatus || {}
)

// Extended AxiosRequestConfig interface
export interface ExtendedRequestConfig extends AxiosRequestConfig {
  mask?: boolean
  apiType?: string
  token?: boolean
  formData?: boolean
  toast?: boolean
  successToast?: boolean
  cancelSource?: CancelTokenSource[] | Record<string, CancelTokenSource>
  cancelSourceId?: string | number
}

// create axios instance
const service: AxiosInstance = axios.create({
  // baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 60000, // 1 minute timeout
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// global loading instance
let gloading: LoadingInstance | null = null

// request interceptor
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const extConfig = config as unknown as ExtendedRequestConfig

    // show loading mask
    if (extConfig.mask) {
      gloading = ElLoading.service()
      delete extConfig.mask
    }

    // Handle headers
    const headers = (extConfig.headers = extConfig.headers || {})

    // handle formData
    if (extConfig.method === 'post' || extConfig.method === 'delete') {
      if (extConfig.formData) {
        const formData = new FormData()
        if (extConfig.data) {
          Object.keys(extConfig.data).forEach((key) => {
            formData.append(key, extConfig.data[key])
          })
        }
        extConfig.data = formData
        delete extConfig.formData
        delete headers['Content-Type'] // let browser set Content-Type and boundary
      }
    } else {
      headers['Accept'] = 'application/json; charset=UTF-8, text/javascript, */*; q=0.01'
    }

    // set token
    if (extConfig.token !== false) {
      const token = localStorage.getItem('token') || storage.xAuthToken
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      } else {
        // compatible with old system, use JSESSIONID
        const jsessionid = jsCookie.get('JSESSIONID')
        if (jsessionid) {
          headers['Authorization'] = `JSESSIONID ${jsessionid}`
        }
      }
      delete extConfig.token
    }
    // handle API path
    if (extConfig.apiType && extConfig.apiType === 'NONE') {
      // do not add any prefix
    } else if (extConfig.url) {
      const apiType = extConfig.apiType || 'DEFAULT'
      const basePath = apiType in BASE_PATH ? BASE_PATH[apiType] : BASE_PATH.DEFAULT
      extConfig.url = basePath + extConfig.url
    }

    // handle request cancel
    registerCancelSource(extConfig)

    return config
  },
  (error) => {
    // close loading mask
    if (gloading) {
      gloading.close()
      gloading = null
    }
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // close loading mask
    if (gloading) {
      gloading.close()
      gloading = null
    }

    const extConfig = response.config as unknown as ExtendedRequestConfig
    const res = response.data

    // handle business error
    if (res && res.success === false) {
      const errMessage =
        res.errorMessage ||
        res.error_message ||
        res.message ||
        res.msg ||
        res.info ||
        'unknown error'

      if (extConfig.toast !== false && extConfig.successToast !== false) {
        ElMessage.error(errMessage)
      }
    }

    // according to custom error code to judge whether the request is successful
    if (res.code && res.code !== 200 && res.code !== 0) {
      // handle business error
      console.error('business error：', res.message || 'unknown error')

      // for example: 401 unauthorized, maybe token expired
      if (res.code === 401 || res.code === 1003) {
        // handle login expired - use debouncedAuthCleanup to avoid frequent cleanup
        import('@/utils').then(({ debouncedAuthCleanup }) => {
          debouncedAuthCleanup(() => {
            // use commonStore to handle logout
            import('@/stores/common').then(({ useCommonStore }) => {
              const commonStore = useCommonStore()
              commonStore.logout()
              window.location.href = '/login'
            })
          })
        })
      }

      if (extConfig.toast !== false) {
        ElMessage.error(res.message || 'unknown error')
      }

      return Promise.reject(new Error(res.message || 'unknown error'))
    } else {
      return res
    }
  },
  (error) => {
    // close loading mask
    if (gloading) {
      gloading.close()
      gloading = null
    }

    // judge whether the request is cancelled
    if (axios.isCancel(error)) {
      console.info('request cancelled:', error.message)
      return Promise.reject(error)
    }

    // handle HTTP error
    if (error.response) {
      const { response } = error
      const extConfig = error.config as unknown as ExtendedRequestConfig
      const data = response.data
      const status = response.status

      console.error(error.config.url, status, JSON.stringify(data))

      let errMessage =
        data.errorMessage ||
        data.message ||
        data.msg ||
        HTTP_STATUS[status as number] ||
        HTTP_STATUS.DEFAULT_ERROR

      switch (status) {
        case 401:
        case 403:
          errMessage = HTTP_STATUS[status] // your login has expired or no permission, please login again
          // 使用防抖机制避免频繁清理
          import('@/utils').then(({ debouncedAuthCleanup }) => {
            debouncedAuthCleanup(() => {
              // 使用commonStore处理登出
              import('@/stores/common').then(({ useCommonStore }) => {
                const commonStore = useCommonStore()
                commonStore.logout()

                if (isProd) {
                  const url = error.request?.responseURL || ''
                  const isSameHost = url.indexOf(window.location.host) !== -1
                  isSameHost && (window.location.href = '/login')
                }
              })
            })
          })
          break

        case 404:
        case 500:
          const code = +data.code || ''
          switch (code) {
            case 1003:
              errMessage = 'You are not logged in or the login has expired' // you are not logged in or your login has expired
              break
            default:
              if (!isProd) {
                errMessage = `${errMessage} - status code: ${code || status}`
              }
              break
          }
          break
      }

      // show error message
      if (extConfig.toast !== false) {
        ElMessage.error(errMessage)
      }
    } else {
      // network error or timeout
      ElMessage.error('network error, please check your network connection')
    }

    return Promise.reject(error)
  }
)

// register cancel request token
function registerCancelSource(config: ExtendedRequestConfig): void {
  if (!config.cancelSource) {
    return
  }

  const CancelToken = axios.CancelToken
  const source = CancelToken.source()

  if (Array.isArray(config.cancelSource)) {
    config.cancelSource.push(source)
    config.cancelToken = source.token
  } else if (typeof config.cancelSource === 'object') {
    const id = config.cancelSourceId || new Date().getTime()
    config.cancelSource[id] = source
    config.cancelToken = source.token
  }
}

// wrap GET request
export function get<T>(url: string, params?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.get(url, { params, ...config })
}

// wrap POST request
export function post<T>(url: string, data?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.post(url, data, config)
}

// wrap PUT request
export function put<T>(url: string, data?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.put(url, data, config)
}

// wrap DELETE request
export function del<T>(url: string, config?: ExtendedRequestConfig): Promise<T> {
  return service.delete(url, config)
}

// create cancel token
export function createCancelToken(): { source: CancelTokenSource; token: any } {
  const source = axios.CancelToken.source()
  return {
    source,
    token: source.token
  }
}

// multi-interface concurrent request
export async function multiRequest<T>(
  requests: Record<string, [string, ExtendedRequestConfig]>
): Promise<Record<string, T>> {
  const requestArr: Promise<any>[] = []
  const keys: string[] = []

  for (const key in requests) {
    const [url, options] = requests[key]
    keys.push(key)
    requestArr.push(service(url, options))
  }

  try {
    const results = await Promise.all(requestArr)
    const resultMap: Record<string, T> = {}

    results.forEach((result, index) => {
      resultMap[keys[index]] = result
    })

    return resultMap
  } catch (error) {
    console.error('multi-interface request error:', error)
    return Promise.reject(error)
  }
}

// export axios instance
export default service
