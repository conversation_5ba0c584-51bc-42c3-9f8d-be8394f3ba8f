import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useSeo } from '@/composables/useSeo'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { setDocumentTitle } from '@/i18n'
import storage from '@/commons/storage'
import { useCommonStore } from '@/stores/common'

// Helper function to safely get meta properties
const getMetaProperty = (meta: any, key: string): string | undefined => {
  return meta && typeof meta[key] === 'string' ? meta[key] : undefined
}

// Import all route modules
const modules = import.meta.glob('./modules/*.ts', { eager: true })

const routes: RouteRecordRaw[] = [
  // Routes will be added by each module
]

// Iterate through modules and register routes
Object.values(modules).forEach((module: any) => {
  if (module.default) {
    module.default(routes)
  }
})
// 404 - Vue Router 4 syntax for catch-all routes
routes.push({
  path: '/:pathMatch(.*)*',
  redirect: '/404'
})
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Merge meta information of parent and child routes
function getMergedMeta(to: any) {
  let merged = { ...to.meta }

  // If there is a parent route, merge the meta of the parent route
  if (to.matched.length > 1) {
    const parentMeta = to.matched[0].meta
    merged = {
      ...parentMeta, // The meta of the parent route is the base
      ...to.meta, // The meta of the child route can override the parent route
      // Special handling of breadcrumbs, if the child route has its own breadcrumbs, use the child route's
      breadcrumb: to.meta.breadcrumb || parentMeta.breadcrumb
    }
  }

  return merged
}

// Global beforeEach guard
router.beforeEach((to, from, next) => {
  // loading
  NProgress.start()
  // setting document title
  let docTitleI18n: string | undefined
  const docTitleI18nModule =
    getMetaProperty(to.meta, 'docTitleI18nModule') ||
    (to.matched && to.matched[0] && getMetaProperty(to.matched[0].meta, 'docTitleI18nModule'))
  // priority: docTitleI18n > i18n > default
  if (docTitleI18nModule)
    docTitleI18n = `${getMetaProperty(to.meta, 'docTitleI18n') || getMetaProperty(to.meta, 'i18n') || 'default'}`

  // Use Pinia store instead of Vuex
  const commonStore = useCommonStore()
  commonStore.setDocumentTitle({
    docTitleI18n,
    docTitleI18nModule,
    title: getMetaProperty(to.meta, 'title'),
    default: 'FleetUp AI'
  })
  if (!to.matched.some((r) => r.meta.ignoreAuth)) {
    // Check multiple authentication states
    const hasAuth = storage.xAuthToken || localStorage.getItem('token')

    if (hasAuth) {
      // Check permissions
      next()
    } else {
      console.warn('No authentication information found, redirecting to login page')
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
    }
  } else {
    next()
  }
  // Set document title
  // document.title = to.meta.title
  //   ? `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE || 'Tech Blog'}`
  //   : import.meta.env.VITE_APP_TITLE || 'Tech Blog'
  // Authentication check
  // const isLoggedIn = !!localStorage.getItem('token')

  // // Routes that require authentication
  // if (!to.meta.ignoreAuth && !isLoggedIn) {
  //   next({ name: 'login', query: { redirect: to.fullPath } })
  // }
  // // Routes that are only accessible to guests
  // else if (to.meta.guest && isLoggedIn) {
  //   next({ name: 'home' })
  // }
  // else {
  //   next()
  // }
})
router.beforeResolve(async (to, from, next) => {
  // Get the current document title state from Pinia store
  const commonStore = useCommonStore()
  setDocumentTitle(commonStore.documentTitle)
  next()
})
router.afterEach((route) => {
  // delete loading
  NProgress.done()
})

export default router
