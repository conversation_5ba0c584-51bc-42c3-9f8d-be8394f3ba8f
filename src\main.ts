import '@/assets/scss/main.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'

import App from './App.vue'
import router from './router'
import { i18n } from './i18n'

import { useTheme } from './composables/useTheme'
import { setupIcons } from '@/plugins/icons'
import fontawesome from './plugins/fontawesome'
import { setupDirectives } from '@/directives'

// AOS animation library
import AOS from 'aos'
import 'aos/dist/aos.css'

// Import Font Awesome CSS
import '@fortawesome/fontawesome-svg-core/styles.css'

const app = createApp(App)
const pinia = createPinia()
const head = createHead()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(fontawesome)
app.use(head)
// setupIcons(app)
setupDirectives(app)

app.mount('#app')

// Initialize theme (must be called after the application is mounted)
const { initTheme } = useTheme()
initTheme()

// Initialize AOS animation
AOS.init({
  duration: 800,
  easing: 'ease-out-cubic',
  once: true,
  offset: 50,
  delay: 100
})
