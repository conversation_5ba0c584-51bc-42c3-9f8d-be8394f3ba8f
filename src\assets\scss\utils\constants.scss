@use "sass:color";

$page-width: 100%;
$page-height: 100vh;
$layout-header-height: 80px;
$layout-nav-height: 80px;

$blue: #009FF0;
$yellow: #FF9004;
$gray: #A7A7A7;
$orange: #FF6C04;
$green: #10AF7E;
$red: #FF7362;
$red-hover: #EC6352;
$gray-hover: #949494;
$font-yellow: $orange;
$font-000: #000;
$font-333: #333;
$font-666: #666;
$font-96: #969696;
$font-5f: #5F5F5F;
$font-a7: #a7a7a7;

$SP-LINK-COLOR: $font-000;
$SP-LINK-HOVER-COLOR: color.adjust($blue, $lightness: -15%);
$SP-LINK-ACTIVE-COLOR: color.adjust($blue, $lightness: -25%);

$SP-border-radius: 2px;
$SP-border-radius-large: 5px;
$SP-border-radius-medium: 4px;
$SP-border-radius-small: 3px;
$SP-border-radius-mini: 2px;
$SP-border-radius-circle: 100%;

/* ************************************
 * 布局相关配置,前缀NI
**************************************** */
$SP-layout-body-bgcolor: #eaf0f7;
$SP-layout-box-bgcolor: #ffffff;
$SP-layout-main-bgcolor: #ffffff;

$SP-layout-border-color: #F2F2F2;
$SP-layout-border-radius: 4px;

$SP-layout-header-bgcolor-hover: #F2F2F2;
$SP-layout-header-text-color: #888888;

$SP-layout-bom-history-color: #333333;
$SP-layout-bom-history-color-hover: #409EFF;
$SP-layout-bom-history-color-active: #409EFF;
$SP-layout-bom-history-bgcolor: #ffffff;
$SP-layout-bom-history-bgcolor-hover: #F2F2F2;
$SP-layout-bom-history-bgcolor-active: #F2F2F2;
$SP-layout-bom-history-border-color: #F2F2F2;

$SP-layout-menu-color: #A7AFB8;
$SP-layout-menu-color-hover: #ffffff;
$SP-layout-menu-color-active: #ffffff;
$SP-layout-menu-bgcolor: #135f5f;
$SP-layout-menu-border-color: color.adjust($SP-layout-menu-bgcolor, $lightness: -12%);
$SP-layout-menu-bgcolor-hover: color.adjust($SP-layout-menu-bgcolor, $lightness: -10%);
$SP-layout-menu-bgcolor-active: color.adjust($SP-layout-menu-bgcolor, $lightness: -20%);
/* ************************************
 * global
**************************************** */
$theme-primary-color : #002e5d;
$color-theme : #002e5d;
$color-theme-light : color.adjust($theme-primary-color, $lightness: 10%);
$color-theme-brighten : color.adjust($theme-primary-color, $lightness: 10%);
$color-theme-light: #33618E;
$color-theme-brighten: #eaaa00;
$color-text-default : #2a2a2a;
$color-text-regular : #606266;
$color-text-secondary : #909399;
$color-text-placeholder : #C0C4CC;
$color-border-base : #DCDFE6;
$color-border-light : #E4E7ED;
$color-border-lighter : #EBEEF5;
$color-border-extraLighter : #F2F6FC;
$color-link : #337ab7;
$color-link-light : #4ca5f2;
$color-success : #67C23A;
$color-warning: color.adjust($theme-primary-color, $lightness: 10%);
$color-danger : #F56C6C;
$color-info : #909399;
$color-disabled : #C0C4CC;
$color-marker-start: #e02d28;
$color-marker-end: #3164a8;
$color-bg-gray: #f1f1f1;
$color-badge-new: #ff0000;
/* ************************************
 * Setting
**************************************** */
$Setting-BGColor-gray: #f2f2f2;
