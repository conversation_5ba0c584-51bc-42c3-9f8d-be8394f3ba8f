# Pinia 事件系统 - 替代 Vue 2 的 $on/$emit

## 概述

在 Vue 3 中，`$on`、`$off` 和 `$once` 实例方法已被移除。我们使用 Pinia 状态管理来实现类似的事件系统功能。

## 架构设计

```
指令/组件 → EventStore → 监听组件
    ↓           ↓           ↓
  emitEvent → state变化 → watch触发
```

## 核心文件

### 1. EventStore (`src/stores/events.ts`)
- 管理全局事件状态
- 提供 `emitEvent` 方法触发事件
- 维护事件历史记录

### 2. useEvents Composable (`src/composables/useEvents.ts`)
- 提供类似 Vue 2 的 `$on`、`$emit`、`$off` API
- 自动清理事件监听器
- 简化使用方式

## 使用方式

### 基础用法

```typescript
// 在组件中
import { useEvents } from '@/composables/useEvents'

export default {
  setup() {
    const { $on, $emit } = useEvents()
    
    // 监听事件（类似 Vue 2 的 this.$on）
    $on('chatHeightChange', (payload) => {
      console.log('高度变化:', payload)
    })
    
    // 触发事件（类似 Vue 2 的 this.$emit）
    $emit('chatHeightChange', { height: '100px' })
    
    return {}
  }
}
```

### 在指令中使用

```typescript
// src/directives/observe-el-height.ts
import { useEventStore } from '@/stores/events'

const observeElHeight: Directive = {
  mounted(el, binding) {
    const eventStore = useEventStore()
    
    const onHeightChange = () => {
      const height = getComputedStyle(el).height
      
      // 触发事件到 Pinia store
      eventStore.emitEvent('chatHeightChange', { 
        height, 
        element: el 
      })
    }
    
    // ... 其他逻辑
  }
}
```

### 在组件中监听

```typescript
// src/views/chat/components/panel-main/index.vue
import { useEvents } from '@/composables/useEvents'

export default {
  setup() {
    const { $on } = useEvents()
    
    onMounted(() => {
      // 监听指令触发的事件
      $on('chatHeightChange', (payload) => {
        console.log('聊天区域高度变化:', payload.height)
        // 执行相应的处理逻辑
        updateScrollState()
      })
    })
    
    return {}
  }
}
```

## API 对比

| Vue 2 | Pinia Events | 说明 |
|-------|--------------|------|
| `this.$emit(event, data)` | `$emit(event, data)` | 触发事件 |
| `this.$on(event, handler)` | `$on(event, handler)` | 监听事件 |
| `this.$off(event)` | `$off(event)` | 取消监听 |
| `this.$once(event, handler)` | 暂未实现 | 一次性监听 |

## 优势

### ✅ 相比 DOM 事件的优势
1. **类型安全**: 完整的 TypeScript 支持
2. **Vue 生态**: 集成 Pinia 状态管理
3. **调试友好**: Vue DevTools 支持
4. **自动清理**: 组件卸载时自动清理监听器
5. **事件历史**: 可查看事件触发历史

### ✅ 相比 Vue 2 $on/$emit 的优势
1. **跨组件通信**: 不受组件层级限制
2. **状态持久化**: 事件状态可以持久化
3. **更好的测试**: 可以直接测试 store 状态
4. **性能优化**: 利用 Pinia 的响应式优化

## 完整示例

```vue
<!-- ParentComponent.vue -->
<template lang="pug">
div
  .content-area(v-observe-el-height="'contentHeightChange'")
    p 这是一些动态内容...
  .status 当前高度: {{ currentHeight }}
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEvents } from '@/composables/useEvents'

const currentHeight = ref('0px')
const { $on } = useEvents()

onMounted(() => {
  // 监听高度变化事件
  $on('contentHeightChange', (payload) => {
    currentHeight.value = payload.height
    console.log('内容高度变化:', payload.height)
  })
})
</script>
```

## 注意事项

1. **事件名称**: 使用有意义的事件名称，避免冲突
2. **内存泄漏**: `useEvents` 会自动清理，但手动调用时记得清理
3. **性能考虑**: 避免过于频繁的事件触发
4. **调试**: 可以在 EventStore 中查看事件历史

## 迁移指南

### Vue 2 → Vue 3 迁移步骤

1. **替换 $on 调用**:
   ```javascript
   // Vue 2
   this.$on('eventName', handler)
   
   // Vue 3
   const { $on } = useEvents()
   $on('eventName', handler)
   ```

2. **替换 $emit 调用**:
   ```javascript
   // Vue 2 (在指令中)
   vnode.context.$emit('eventName', data)
   
   // Vue 3
   const eventStore = useEventStore()
   eventStore.emitEvent('eventName', data)
   ```

3. **清理事件监听**:
   ```javascript
   // Vue 2
   this.$off('eventName')
   
   // Vue 3
   const { $off } = useEvents()
   $off('eventName')
   // 或者依赖自动清理（推荐）
   ```

这样就完美实现了您的想法，使用 Pinia 来替代 Vue 2 的 `$on`/`$emit` 功能！ 