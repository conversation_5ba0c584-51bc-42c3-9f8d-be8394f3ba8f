@mixin x-animation($value) {
  -webkit-animation: $value;
  -moz-animation: $value;
  -o-animation: $value;
  animation: $value;
}

@mixin x-appearance($value) {
  -webkit-appearance: $value;
  -moz-appearance: $value;
  appearance: $value;
}

@mixin x-backface-visibility($value: visible) {
  -webkit-backface-visibility: $value;
  -moz-backface-visibility: $value;
  -ms-backface-visibility: $value;
  backface-visibility: $value;
}

@mixin x-background-clip($mode: border-box) {
  $xmode: null;

  @if $mode == border-box {
    $xmode: border;
  }


  @if $mode == padding-box {
    $xmode: padding;
  }


  @if $mode == content-box {
    $xmode: content;
  }


  -moz-background-clip: $xmode;
  -webkit-background-clip: $xmode;
  background-clip: $mode;
}

@mixin x-background-origin($mode: border-box) {
  $xmode: null;

  @if $mode == border-box {
    $xmode: border;
  }


  @if $mode == padding-box {
    $xmode: padding;
  }


  @if $mode == content-box {
    $xmode: content;
  }


  -moz-background-origin: $xmode;
  -webkit-background-origin: $xmode;
  background-origin: $mode;
}

@mixin x-background-retina($img_1x, $img_2x, $width_1x, $height_1x) {
  background-image: url($img_1x);


  @media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min--moz-device-pixel-ratio: 2), screen and (-o-min-device-pixel-ratio: 2 / 1), screen and (min-device-pixel-ratio: 2), screen and (min-resolution: 192dpi), screen and (min-resolution: 2dppx) {
    background-image: url($img_2x);
    @include x-background-size($width_1x $height_1x);
  }
}


@mixin x-background-size($size) {
  -webkit-background-size: $size;
  -moz-background-size: $size;
  -o-background-size: $size;
  background-size: $size;
}

@mixin x-border-radius($radius, $direction: all) {
  @if $direction == all {
    -moz-border-radius: $radius;
    -webkit-border-radius: $radius;
    border-radius: $radius;
  }


  @if $direction == top {
    -moz-border-radius: $radius $radius 0 0;
    -webkit-border-radius: $radius $radius 0 0;
    border-radius: $radius $radius 0 0;
  }


  @if $direction == right {
    -moz-border-radius: 0 $radius $radius 0;
    -webkit-border-radius: 0 $radius $radius 0;
    border-radius: 0 $radius $radius 0;
  }


  @if $direction == bottom {
    -moz-border-radius: 0 0 $radius $radius;
    -webkit-border-radius: 0 0 $radius $radius;
    border-radius: 0 0 $radius $radius;
  }


  @if $direction == left {
    -moz-border-radius: $radius 0 0 $radius;
    -webkit-border-radius: $radius 0 0 $radius;
    border-radius: $radius 0 0 $radius;
  }
}


@mixin x-border-triangle($borderWidth, $borderColor, $direction) {
  $default-border-style: solid;

  width: 0;
  height: 0;
  overflow: hidden;
  border-width: $borderWidth;
  border-style: $default-border-style;
  _border-style: dashed;
  border-color: transparent;

  @if $direction == top {
    border-bottom-color: $borderColor;
    _border-bottom-style: $default-border-style;
  }


  @if $direction == right {
    border-left-color: $borderColor;
    _border-left-style: $default-border-style;
  }


  @if $direction == bottom {
    border-top-color: $borderColor;
    _border-top-style: $default-border-style;
  }


  @if $direction == left {
    border-right-color: $borderColor;
    _border-right-style: $default-border-style;
  }


  @if $direction == topleft {
    border-top-color: $borderColor;
    border-left-color: $borderColor;
    _border-top-style: $default-border-style;
    _border-left-style: $default-border-style;
  }


  @if $direction == topright {
    border-top-color: $borderColor;
    border-right-color: $borderColor;
    _border-top-style: $default-border-style;
    _border-right-style: $default-border-style;
  }


  @if $direction == bottomleft {
    border-bottom-color: $borderColor;
    border-left-color: $borderColor;
    _border-bottom-style: $default-border-style;
    _border-left-style: $default-border-style;
  }


  @if $direction == bottomright {
    border-bottom-color: $borderColor;
    border-right-color: $borderColor;
    _border-bottom-style: $default-border-style;
    _border-right-style: $default-border-style;
  }
}


@mixin x-box-shadow($params) {
  -moz-box-shadow: $params;
  -webkit-box-shadow: $params;
  box-shadow: $params;
}

@mixin x-box-sizing($model) {
  -webkit-box-sizing: $model;
  -moz-box-sizing: $model;
  box-sizing: $model;
}

@mixin x-clearfix() {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }

  *zoom: 1;
}

@mixin x-font-face($fontName, $noSuffixFontUrl) {
  @font-face {
    font-family: $fontName + "";
    src: url($noSuffixFontUrl + ".eot");
    src: url($noSuffixFontUrl + ".eot?#iefix") format("embedded-opentype"),
    url($noSuffixFontUrl + ".woff") format("woff"),
    url($noSuffixFontUrl + ".ttf") format("truetype"),
    url($noSuffixFontUrl + ".svg#svgFontName") format("svg");
  }
}


@mixin x-grayscale() {
  /* for Chrome, Safari, Opera */
  -webkit-filter: grayscale(100%);
  /* CSS3 standard usage */
  filter: grayscale(100%);
  /* for FF, data-uri generate from gray.svg */
  filter: url('data:image/svg+xmlbase64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgICA8ZmlsdGVyIGlkPSJncmF5c2NhbGUiPgogICAgICAgIDxmZUNvbG9yTWF0cml4IHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwLjMzMzMgMC4zMzMzIDAuMzMzMyAwIDAgMC4zMzMzIDAuMzMzMyAwLjMzMzMgMCAwIDAuMzMzMyAwLjMzMzMgMC4zMzMzIDAgMCAwIDAgMCAxIDAiLz4KICAgIDwvZmlsdGVyPgo8L3N2Zz4=#grayscale');
  /* for IE6~9, disable stylus convert gray to #808080 by stylus's unquote method */
  filter: unquote("gray");
}

@mixin x-hyphens($mode: auto) {
  -ms-word-break: break-all;
  word-break: break-all;
  word-break: break-word;
  -webkit-hyphens: $mode;
  -moz-hyphens: $mode;
  -ms-hyphens: $mode;
  hyphens: $mode;
}

@mixin x-inline-block() {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}

@mixin x-linear-gradient($direction, $startColor, $endColor) {
  $xdirection: null;
  $ie-direction: null;

  @if join(",", $direction) == "to,bottom" {
    $xdirection: top;
    $ie-direction: 0;
  }

  @if join(",", $direction) == "to,right" {
    $xdirection: left;
    $ie-direction: 1;
  }


  background: -webkit-linear-gradient($xdirection, $startColor, $endColor);
  background: -moz-linear-gradient($xdirection, $startColor, $endColor);
  background: -ms-linear-gradient($xdirection, $startColor, $endColor);
  background: -o-linear-gradient($xdirection, $startColor, $endColor);
  background: linear-gradient($direction, $startColor, $endColor);
  filter: unquote("progid:DXImageTransform.Microsoft.gradient(startcolorstr=" + $startColor + ",endcolorstr=" + $endColor + ",gradientType=" + $ie-direction + ")");
}

@mixin x-min-height($height) {
  min-height: $height;
  _height: $height;
}

@mixin x-opacity($opacity) {
  $opacityIE: $opacity * 100;
  -ms-filter: unquote('progid=DXImageTransform.Microsoft.Alpha(Opacity=' + $opacityIE + ')');
  filter: unquote('alpha(opacity=' + $opacityIE + ')');
  opacity: $opacity;
}

@mixin x-perspective-origin($origin: 50% 50%) {
  -webkit-perspective-origin: $origin;
  -moz-perspective-origin: $origin;
  perspective-origin: $origin;
}

@mixin x-perspective($value: none) {
  -webkit-perspective: $value;
  -moz-perspective: $value;
  perspective: $value;
}


@mixin x-placeholder($color) {
  ::-moz-placeholder {
    color: $color;
    opacity: 1;
  }
  :-moz-placeholder {
    color: $color;
  }
  :-ms-input-placeholder {
    color: $color;
  }
  ::-webkit-input-placeholder {
    color: $color;
  }
}


@mixin x-radial-gradient() {
  $param: unquote($join (',', $arguments));
  background: -webkit-radial-gradient($param);
  background: -moz-radial-gradient($param);
  background: -ms-radial-gradient($param);
  background: radial-gradient($param);
}

@mixin x-text-overflow($cutway: clip) {
  overflow: hidden;
  text-overflow: $cutway;
  white-space: nowrap;
}

@mixin x-transform-origin($value: 50% 50% 0) {
  -webkit-transform-origin: $value;
  -moz-transform-origin: $value;
  -ms-transform-origin: $value;
  transform-origin: $value;
}

@mixin x-transform-style($value: flat) {
  -webkit-transform-style: $value;
  -moz-transform-style: $value;
  transform-style: $value;
}

@mixin x-transform($value) {
  -webkit-transform: $value;
  -moz-transform: $value;
  -ms-transform: $value;
  -o-transform: $value;
  transform: $value;
}

@mixin x-transition($value) {
  -webkit-transition: $value;
  -moz-transition: $value;
  -o-transition: $value;
  transition: $value;
}

@mixin x-user-select($mode: text) {
  -webkit-user-select: $mode;
  -moz-user-select: $mode;
  -ms-user-select: $mode;
  user-select: $mode;
}
