# Manual Tests 手动测试组件

这个目录包含了项目中的手动测试组件，用于开发和调试目的。这些组件提供了交互式的测试界面，方便开发者验证功能。

## 📁 目录结构

```
tests/manual/
├── README.md              # 本说明文档
├── admin-info-test.vue    # 管理员信息测试组件
├── directives-test.vue    # Vue 指令功能测试组件
└── c-scroll-test.vue      # CScroll 组件功能测试组件
```

## 🧪 测试组件说明

### admin-info-test.vue
测试管理员信息相关的 Pinia store 功能：
- 管理员信息获取
- 状态管理验证
- API 调用测试

### directives-test.vue
测试项目中所有自定义 Vue 指令的功能：
- `v-img-url` - 图片 URL 处理指令
- `v-observe-el-height` - 元素高度观察指令
- `v-avatar` - 头像显示指令
- `v-coming-soon` - 即将推出占位符指令
- `v-countdown` - 倒计时指令

### c-scroll-test.vue
测试 CScroll 自定义滚动组件的功能：
- 基础滚动功能（滚动到顶部/底部/中间）
- 动态内容添加和删除
- RTL 语言支持测试
- 滚动状态检测

## 🚀 使用方法

### 开发环境中使用

这些组件仅用于开发和调试，不会出现在生产环境的路由中。

#### 方法1：直接在代码中导入
```vue
<template>
  <DirectivesTest />
</template>

<script setup>
import DirectivesTest from '@/tests/manual/directives-test.vue'
</script>
```

#### 方法2：临时添加开发路由
在 `src/router/modules/main.ts` 中临时添加路由（记得完成测试后删除）：

```typescript
// 仅开发环境使用
if (import.meta.env.DEV) {
  routes.push({
    path: '/dev/directives-test',
    name: 'dev-directives-test',
    component: () => import('@/tests/manual/directives-test.vue'),
    meta: { ignoreAuth: true }
  })
}
```

### 单元测试中使用

这些组件也可以作为单元测试的参考：

```typescript
// tests/unit/directives.spec.ts
import { mount } from '@vue/test-utils'
import DirectivesTest from '@/tests/manual/directives-test.vue'

describe('Directives', () => {
  it('should register all directives correctly', () => {
    const wrapper = mount(DirectivesTest)
    // 测试逻辑...
  })
})
```

## ⚠️ 注意事项

1. **不要在生产路由中包含这些组件**
2. **测试完成后记得清理临时路由**
3. **这些组件包含调试日志，请在控制台查看详细信息**
4. **如需修改测试用例，请确保不影响主业务逻辑**

## 🔧 开发指南

### 添加新的测试组件

1. 在 `tests/manual/` 目录下创建新的 `.vue` 文件
2. 使用描述性的文件名，如 `feature-name-test.vue`
3. 在组件中添加详细的注释和调试信息
4. 更新本 README 文档

### 测试组件命名规范

- 使用 kebab-case 命名：`feature-test.vue`
- 文件名应该清楚表明测试的功能
- 组件名使用 PascalCase：`FeatureTest`

## 📚 相关文档

- [Vue 3 指令文档](https://vuejs.org/guide/reusability/custom-directives.html)
- [Pinia 状态管理文档](https://pinia.vuejs.org/)
- [项目测试指南](../README.md) 