@use "sass:map";
@use "./_themes.scss" as *;

// 声明全局变量以兼容 Dart Sass 2.0.0
$theme-map: null;

//遍历主题map
@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    //!global 把局部变量强升为全局变量
    $theme-map: $theme-map !global;
    //判断html的data-theme的属性值  #{}是sass的插值表达式
    //& sass嵌套里的父容器标识   @content是混合器插槽，像vue的slot
    [data-theme="#{$theme-name}"] & {
      @content;
    }
  }
}


//声明一个根据Key获取颜色的function
@function themed($key) {
  @return map.get($theme-map, $key);
}

//获取背景颜色
@mixin theme_bg_color($color) {
  @include themeify {
    background-color: themed($color);
  }
}

//获取字体颜色
@mixin theme_font_color($color) {
  @include themeify {
    color: themed($color);
  }
}

//获取边框颜色
@mixin theme_border_color($color) {
  @include themeify {
    border-color: themed($color);
  }
}
@mixin border_color_left_header_arrow($color) {
  @include themeify {
    border-left: 13px solid themed($color);
  }
}

@mixin border_color_left($color) {
  @include themeify {
    border-left: 13px solid themed($color);
  }
}

@mixin theme_border($width, $type, $color, $position:"all") {
  @include themeify {
    @if $position == left {
      border-left: $width $type themed($color);
    }
    @else if $position == top {
      border-top: $width $type themed($color);
    }
    @else if $position == right {
      border-right: $width $type themed($color);
    }
    @else if $position == bottom {
      border-bottom: $width $type themed($color);
    }
    @else {
      border: $width $type themed($color);
    }

  }
}
