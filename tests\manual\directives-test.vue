<template lang="pug">
.directives-test
  h1 Vue 3 指令功能测试
  
  .test-section
    h2 v-img-url 指令测试
    .test-item
      p 测试图片加载和错误处理：
      .image-tests
        .image-test
          p 正常图片加载：
          img(v-img-url="'/res/imgs/home/<USER>'" data-prefix="local" style="width: 100px; height: 100px; border: 1px solid #ddd;")
        .image-test  
          p 错误图片处理：
          img(v-img-url="'/nonexistent.jpg'" data-prefix="local" style="width: 100px; height: 100px; border: 1px solid #ddd;")
      
  .test-section
    h2 v-observe-el-height 指令测试
    .test-item
      p 测试高度变化观察：
      .height-test(v-observe-el-height="'testHeightChange'" style="background: #f0f0f0; padding: 20px; margin: 10px 0; border: 1px solid #ccc;")
        p 这是一个会改变高度的元素
        button(@click="toggleContent") {{ showContent ? '隐藏内容' : '显示内容' }}
        .dynamic-content(v-if="showContent")
          p 这是动态内容
          p 当这个内容显示/隐藏时，会触发高度变化事件
          p 可以在控制台查看事件触发情况
          
  .test-section
    h2 v-avatar 指令测试
    .test-item
      p 测试头像显示：
      .avatar-tests
        .avatar-test
          p 有头像图片：
          .avatar-demo(v-avatar="'/res/avatar.gif'" full-name="Test User" style="width: 50px; height: 50px; border-radius: 50%; background: #ddd; display: flex; align-items: center; justify-content: center; margin: 10px 0;")
        .avatar-test
          p 无头像（显示首字母）：
          .avatar-demo(v-avatar="" full-name="John Doe" style="width: 50px; height: 50px; border-radius: 50%; background: #007bff; color: white; display: flex; align-items: center; justify-content: center; margin: 10px 0;")
      
  .test-section
    h2 v-coming-soon 指令测试
    .test-item
      p 测试即将推出占位符：
      .coming-soon-demo(v-coming-soon style="width: 200px; height: 100px; margin: 10px 0;")

  .test-section
    h2 v-countdown 指令测试
    .test-item
      p 测试倒计时功能：
      .countdown-demo(v-countdown="countdownValue" formatter="HH:mm:ss" style="font-size: 24px; font-weight: bold; color: #007bff; padding: 20px; background: #f8f9fa; border-radius: 8px; margin: 10px 0;")
      button(@click="resetCountdown") 重置倒计时

  .debug-info
    h3 调试信息
    p 请打开浏览器开发者工具查看指令加载和事件触发的详细日志
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const showContent = ref(false)
const countdownValue = ref(60000) // 60秒倒计时

const toggleContent = () => {
  showContent.value = !showContent.value
  console.log('Content toggled:', showContent.value)
}

const resetCountdown = () => {
  countdownValue.value = 60000
  console.log('Countdown reset to 60 seconds')
}

onMounted(() => {
  console.log('Directives test component mounted')
  console.log('Available directives should be logged during app initialization')
})
</script>

<style lang="scss" scoped>
.directives-test {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-section {
  margin: 40px 0;
  padding: 24px;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  background: #fafbfc;
}

.test-item {
  margin: 20px 0;
}

h1 {
  color: #1f2937;
  text-align: center;
  margin-bottom: 40px;
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  color: #374151;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 12px;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

h3 {
  color: #4b5563;
  font-size: 1.2rem;
  margin-bottom: 16px;
}

p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12px;
}

button {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background: #2563eb;
  }

  &:active {
    background: #1d4ed8;
  }
}

.image-tests,
.avatar-tests {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.image-test,
.avatar-test {
  flex: 1;
  min-width: 200px;
}

.dynamic-content {
  background: #ffffff;
  padding: 20px;
  margin: 15px 0;
  border-radius: 8px;
  border-left: 4px solid #10b981;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.debug-info {
  margin-top: 40px;
  padding: 20px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
}

.countdown-demo {
  display: inline-block;
}
</style>