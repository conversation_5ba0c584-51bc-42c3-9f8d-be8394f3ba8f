---
description: 
globs: 
alwaysApply: true
---
你是一位 TypeScript、Node.js、Vite、Vue.js、Vue Router、Pinia、VueUse、Headless UI、Element Plus 和 Tailwind 方面的专家，对这些技术的最佳实践和性能优化有深入理解。

# 代码风格与结构

* 编写简洁、可维护且技术准确的 TypeScript 代码，并附上相关示例。
* 使用函数式和声明式编程模式，避免使用类。
* 优先通过迭代和模块化来遵循 DRY 原则，避免代码重复。
* 使用带助动词的描述性变量名（如 `isLoading`、`hasError`）。
* 系统化组织文件：每个文件仅包含相关内容，如导出的组件/子组件、辅助函数、静态资源和类型定义。
* 每个Vue组件，文件夹名使用的是组件的名称，文件夹里面固定有index.vue和index.scss，样式写入index.scss文件，使用scss文件引入形式，在index.vue里的style标签引入index.scss，样式不要直接写在index.vue里面，保持项目的整洁和结构化，方便维护

- **遵循 Vue 官方风格指南**
  - 组件名称使用多词组合（PascalCase）
  - Props 定义应尽量详细，至少指定类型
  - 组件属性顺序：name, props, emits, setup/data, computed, methods
  - 使用 Composition API 语法
  - Vue 中的template模板必须使用Pug语言书写

- **使用 ESLint 和 Prettier 强制代码风格统一**
  - 基于 Vue 官方推荐配置 `eslint-plugin-vue`
  - 使用 `.eslintrc.js` 和 `.prettierrc.js` 统一团队代码风格
  - 将代码格式化集成到 Git 提交流程（pre-commit hook）

- **命名约定**
  - 组件文件名：PascalCase（如 `ArticleList.vue`）
  - 目录名：kebab-case（如 `common-components`）
  - 变量/函数：camelCase（如 `getUserInfo`）
  - CSS 类名：kebab-case（如 `.nav-item`）
  - Store 模块名：camelCase（如 `userStore`）

- **注释规范**
  - 复杂业务逻辑必须添加注释
  - 公共函数需添加参数说明
  - 使用 JSDoc 风格注释 `/**  */` 
  - CSS 模块分组需加注释

# 命名约定

* 目录使用小写并以连字符分隔（例如 `components/auth-wizard`）。
* 函数优先使用具名导出（named exports）。

# TypeScript 使用

* 所有代码均使用 TypeScript；优先使用 `interface` 而非 `type`，以便扩展和声明合并。
* 避免使用 `enum`，改用映射对象（Map／对象字面量）以获得更好的类型安全性和灵活性。
* 使用带有 TypeScript 接口定义的函数式组件。

# 语法与格式

* 纯函数使用 `function` 关键字，以便利用提升（hoisting）和保持清晰。
* 始终采用 Vue Composition API 的 `<script setup>` 写法。

# UI 与样式

* 使用 Element Plus 实现组件和样式。

# 性能优化

* 在合适场景下利用 VueUse 提供的工具函数，提升响应性和性能。
* 将异步组件包裹在 `<Suspense>` 中，并提供回退（fallback）UI。
* 对非关键组件使用动态按需加载。
* 优化图片使用：采用 WebP 格式、包含尺寸属性、启用懒加载。
* 在 Vite 构建时制定合理的拆包策略（如代码分割），以减小打包体积。

# 核心指标优化

* 使用 Lighthouse、WebPageTest 等工具持续优化 Web 核心指标（LCP、CLS、FID）。

