import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import legacy from '@vitejs/plugin-legacy'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import tailwind from '@tailwindcss/vite'
import postcssPlugin from '@tailwindcss/postcss'
import autoprefixer from 'autoprefixer'
import i18nMergePlugin from './plugins/vite-i18n-plugin'
import { createHtmlPlugin } from 'vite-plugin-html'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import viteImagemin from 'vite-plugin-imagemin'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd())

  return {
    base: '/',
    plugins: [
      i18nMergePlugin(),
      vue({
        template: {
          compilerOptions: {
            // Handle compatibility issues with special characters in Tailwind CSS within pug
            isCustomElement: (tag) => tag.includes('_')
          }
        }
      }),
      vueJsx(),
      vueDevTools(),
      // Legacy browser support
      legacy({
        targets: ['ie >= 11', 'chrome 52', 'safari 10', 'edge >= 79'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        modernPolyfills: true,
        renderLegacyChunks: true,
        polyfills: [
          'es.promise',
          'es.array.iterator',
          'es.object.assign',
          'es.promise.finally',
          'es/map',
          'es/set',
          'es.array.includes',
          'es.string.includes'
        ]
      }),
      viteImagemin({
        gifsicle: {
          optimizationLevel: 7,
          interlaced: false
        },
        optipng: {
          optimizationLevel: 7
        },
        mozjpeg: {
          quality: 60
        },
        pngquant: {
          quality: [0.7, 0.8],
          speed: 4
        },
        svgo: {
          plugins: [
            {
              name: 'removeViewBox'
            },
            {
              name: 'removeEmptyAttrs',
              active: false
            }
          ]
        },
        webp: {
          quality: 70
        }
      }),
      Icons({
        autoInstall: true,
        compiler: 'vue3'
      }),
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto import icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ]
      }),
      Components({
        dirs: ['src'],
        extensions: ['vue'],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto register icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ],
        deep: true,
        directoryAsNamespace: true
      }),
      tailwind(),
      createHtmlPlugin({
        minify: true,
        inject: {
          data: {
            TITLE: env.VITE_APP_TITLE || 'FleetUp Search AI and Analytics AI'
          }
        }
      })
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/scss/imports.scss" as *;@use "@/assets/scss/themes/_mixin.scss" as *;@use "@/assets/scss/element/index.scss" as *;`
        }
      },
      postcss: {
        plugins: [postcssPlugin(), autoprefixer()]
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@res': fileURLToPath(new URL('public/res', import.meta.url)),
        '@data': fileURLToPath(new URL('public/data', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0',
      port: 8016,
      proxy: {
        '/api': {
          target: 'https://chatbot.dev.fleetup.net/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/service_api': {
          target: 'https://chatbotservice.ai.fleetup.cc/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/message_api': {
          target: 'http://************/chatservice/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/szdev/, '')
        }
      }
    },
    build: {
      outDir: './dist',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: [
            'console.log',
            'console.info',
            'console.debug',
            'console.time',
            'console.timeEnd'
          ]
        }
      },
      rollupOptions: {
        input: {
          main: fileURLToPath(new URL('./index.html', import.meta.url))
        },
        output: {
          // Global variable name mapping, equivalent to globals config in webpack externals
          globals: {},
          // JS file output configuration
          chunkFileNames: (chunkInfo) => {
            return 'static/js/[name]-[hash].js'
          },
          entryFileNames: 'static/js/[name]-[hash].js',
          // Asset file output configuration
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') ?? []
            const ext = info[info.length - 1]

            // CSS files
            if (/\.(css)$/i.test(assetInfo.name ?? '')) {
              return 'static/css/[name]-[hash].[ext]'
            }

            // Image files
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name ?? '')) {
              return 'static/imgs/[name]-[hash].[ext]'
            }

            // Font files
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name ?? '')) {
              return 'static/fonts/[name]-[hash].[ext]'
            }

            // Other asset files
            return 'static/[name]-[hash].[ext]'
          }
        }
      }
    },
    assetsInclude: ['**/*.jpg', '**/*.png', '**/*.svg', '**/*.webp']
  }
})
