@font-face{
  font-family: 'iconfont';
  src: url('@/assets/fonts/iconfont.woff') format('woff'), url('@/assets/fonts/iconfont.ttf') format('truetype');
}


.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-data-conversion:before {
  content: "\e6cd";
}

.icon-ding:before {
  content: "\e651";
}

.icon-cai:before {
  content: "\e603";
}

.icon-bottom:before {
  content: "\e666";
}

.icon-refresh:before {
  content: "\e6a7";
}

.icon-header-close:before {
  content: "\e6cb";
}

.icon-delete:before {
  content: "\e68c";
}

.icon-create:before {
  content: "\e695";
}

.icon-edit:before {
  content: "\e64f";
}

.icon-user-avatar:before {
  content: "\e601";
}

.icon-chat:before {
  content: "\e669";
}

.icon-send:before {
  content: "\e719";
}

.icon-feedback:before {
  content: "\e600";
}

.icon-love:before {
  content: "\e610";
}
