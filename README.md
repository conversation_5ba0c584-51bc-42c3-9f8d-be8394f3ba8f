# FleetUp AI Panel Website

该项目是 FleetUp 在2025年的重要产品升级模块，目标是托管运输车队业务数据，通过自然语言查询提供智能化分析和搜索组件

## 🚀 特性

- 📱 响应式设计，完美适配各种设备
- 🌍 多语言国际化支持
- ⚡️ 现代化构建工具 Vite，快速的开发体验
- 🎨 集成 Element Plus 和 TailwindCSS，优雅的 UI 设计
- 💫 丰富的动画效果 (GSAP, AOS)
- 🔍 SEO 优化
- 📦 自动化的图片优化
- ✅ 完整的测试支持

## 🛠 技术栈

- **核心框架：** Vue 3
- **开发语言：** TypeScript
- **构建工具：** Vite
- **UI 框架：** Element Plus
- **CSS 预处理：** SASS/SCSS
- **状态管理：** Pinia
- **路由管理：** Vue Router
- **动画库：** GSAP, AOS
- **轮播组件：** Swiper
- **图标支持：** FontAwesome, Element Plus Icons，iconfont(custom)
- **测试框架：** Vitest, Cypress
- **代码规范：** ESLint, Prettier
- **模板引擎：** Pug

## 📦 项目结构

```
src/
├── api/         # API 接口
├── assets/      # 静态资源
│   ├── css/     # 样式文件
│   └── images/  # 图片资源
├── components/  # 通用组件
├── composables/ # 组合式函数
├── conf/        # 配置文件
├── i18n/        # 国际化
│   └── langs/   # 语言包
├── plugins/     # 插件
├── router/      # 路由配置
├── stores/      # Pinia 状态管理
├── types/       # TypeScript 类型定义
├── utils/       # 工具函数
├── views/       # 页面视图
├── App.vue      # 根组件
└── main.ts      # 应用入口
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装

```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd fleetup-AI-search

# 安装依赖
npm install
```

### 开发

```bash
# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

### 测试

```bash
# 单元测试
npm run test:unit

# 单元测试覆盖率报告
npm run test:unit:coverage

# E2E 测试
npm run test:e2e

# 开发模式下的 E2E 测试
npm run test:e2e:dev
```

### 构建

```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 📝 开发指南

### 代码规范

- 使用 ESLint 和 Prettier 进行代码规范
- 遵循 Vue 3 组合式 API 的最佳实践
- 使用 TypeScript 进行类型检查
- 遵循组件化开发原则

### 样式指南

- 使用 SCSS 进行自定义样式
- 遵循响应式设计原则
- 使用 Element Plus 组件库

### 国际化

- 使用 vue-i18n 进行国际化
- 语言文件位于 `src/i18n/langs` 目录
- 支持动态语言切换

### SEO 优化

- 使用 @vueuse/head 管理头部元信息
- 自动生成站点地图
- 图片优化和延迟加载

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request


## 👥 团队

FleetUp 开发团队
