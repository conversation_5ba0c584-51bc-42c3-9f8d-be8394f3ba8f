import storage from '@/commons/storage'
import { API as configApi } from '@/conf'
import qs from 'querystring'
import { ElMessage } from 'element-plus'

// Type definitions
interface RequestParams {
  data?: any
  apiType?: string
  cb?: (params: CallbackParams) => void
  quesionIndex?: number
  index?: number
  toast?: boolean
  token?: boolean
  method: string
  formData?: boolean
  headers?: Record<string, string>
  body?: string
  [key: string]: any
}

interface CallbackParams {
  msg: string
  chatId: string
  answerIndex?: number
  quesionIndex?: number
}

interface StreamFetchResult {
  stream: ReadableStream
  data: string
}

interface StreamFetchError {
  type: 'fetchError'
  data: any
  name?: string
}

const isProd = process.env.NODE_ENV === 'production'
const BASE_PATH = isProd ? configApi.production : configApi.development

export const streamFetch = async (url: string, requestParams: RequestParams): Promise<StreamFetchResult> => {
  const { data, apiType, cb, quesionIndex, index: answerIndex, toast } = requestParams
  delete requestParams.apiType
  delete requestParams.cb
  delete requestParams.quesionIndex
  delete requestParams.index
  
  const decoder = new TextDecoder()
  const headers: Record<string, string> = {}
  
  if (requestParams.token) {
    headers.Authorization = `Bearer ${storage.xAuthToken}`
    delete requestParams.token
  }
  
  if (requestParams.method.toLowerCase() === 'post') {
    if (requestParams.formData) {
      headers['Content-Type'] = 'application/x-www-form-urlencoded'
      requestParams.body = qs.stringify({
        ...data
      })
      delete requestParams.formData
      delete requestParams.data
    } else {
      headers['Content-Type'] = 'application/json'
      requestParams.body = JSON.stringify(data)
    }
  }
  
  if (requestParams.method.toLowerCase() === 'get') {
    url = url + '?' + new URLSearchParams(data).toString()
  }
  
  requestParams.headers = Object.assign({}, headers, requestParams.headers)

  if (apiType !== 'NONE') {
    url = ((apiType && (BASE_PATH as any)[apiType]) || (BASE_PATH as any)['DEFAULT']) + url
  }
  
  return new Promise<StreamFetchResult>((resolve, reject) => {
    fetch(url, requestParams).then(async (res: Response) => {
      if (!res.ok) {
        throw res
      } else {
        return res.body
      }
    })
      .then((res: ReadableStream<Uint8Array> | null) => {
        if (!res) {
          throw new Error('Response body is null')
        }
        
        try {
          const stream = new ReadableStream({
            async start (controller: ReadableStreamDefaultController) {
              const reader = res.getReader()
              let result = ''

              function push (): void {
                // "done" is a Boolean and value a "Uint8Array"
                reader.read().then(({ done, value }: ReadableStreamReadResult<Uint8Array>) => {
                  // If there is no more data to read
                  if (done) {
                    controller.close()
                    resolve({ stream, data: result })
                    return
                  }
                  
                  if (value) {
                    const text = decoder.decode(value)
                    result += text
                    // Call callback if provided
                    cb && cb({ msg: text, chatId: data.tid, answerIndex, quesionIndex })
                    // Get the data and send it to the browser via the controller
                    controller.enqueue(value)
                  }
                  
                  // Check chunks by logging to the console
                  push()
                }).catch((e: any) => {
                  const error: StreamFetchError = { type: 'fetchError', data: e, name: e && e.name }
                  reject(error)
                })
              }

              push()
            }
          })
          return stream
        } catch (e) {
          throw e
        }
      })
      .catch((e: any) => {
        let errorMsg = ''
        if (Object.prototype.toString.apply(e).toLowerCase().indexOf('string') !== -1) {
          errorMsg = e
        } else {
          const { status, statusText } = e
          if (status === undefined) {
            errorMsg = e.toString()
          } else {
            errorMsg = `${status}: ${statusText}`
          }
        }
        
        if (toast !== false && e.name !== 'AbortError') {
          ElMessage({
            type: 'error',
            message: (storage.httpStatus && storage.httpStatus['DEFAULT_ERROR']) || 'Not connected to the internet'
          })
        }
        
        const eRes: StreamFetchError = { data: e, type: 'fetchError', name: e && e.name }
        return reject(eRes)
      })
  })
} 