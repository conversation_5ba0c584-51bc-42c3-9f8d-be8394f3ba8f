<template lang="pug">
footer.c-footer
  .container.mx-auto.px-4
    .footer-content
      .footer-info
        .company-info
          img.company-logo(:src="logo" :alt="t('app.common.companyName')")
          p.company-name {{t('app.common.companyName')}}
        p.copyright {{t('app.common.footer.copyRight', {year: currentYear})}}

      .footer-links
        .link-group
          h3.link-title {{t('app.common.footer.contact')}}
          a.footer-link(:href="`tel:${contactInfo.salesPhoneCountryCode}${contactInfo.salesPhone}`") {{`${contactInfo.salesPhoneCountryCode} ${contactInfo.salesPhone}`}}
          a.footer-link(:href="`mailto:${contactInfo.salesEmail}`") {{contactInfo.salesEmail}}
          p.footer-text {{t('app.common.footer.address')}}

        .link-group
          h3.link-title {{t('app.common.footer.linkTitle')}}
          .link-mul
            .link-item
              a.footer-link(href="/about") {{t('app.common.footer.linkAbout')}}
              a.footer-link(href="/service") {{t('app.common.footer.linkService')}}
              a.footer-link(href="/contact") {{t('app.common.footer.linkContact')}}
            .link-item
              a.footer-link(href="/safety") {{t('app.common.extraLinks.safety')}}
              a.footer-link(href="/licenses") {{t('app.common.extraLinks.licenses')}}
              a.footer-link(:href="contactInfo.recruitUrl" target="_blank") {{t('app.common.extraLinks.careers')}}

      .social-links
        h3.link-title {{t('app.common.footer.socialTitle')}}
        .social-icons
          a.social-link(
            href="https://www.xiaohongshu.com"
            target="_blank"
            rel="noopener noreferrer"
            title="RedNote"
          )
            i.iconfont.icon-fontxiaohongshu
          a.social-link(
            href="https://www.tiktok.com"
            target="_blank"
            rel="noopener noreferrer"
            title="Tiktok"
          )
            font-awesome-icon(:icon="['fab', 'tiktok']")
          a.social-link(
            href="https://linkedin.com"
            target="_blank"
            rel="noopener noreferrer"
            title="LinkedIn"
          )
            font-awesome-icon(:icon="['fab', 'linkedin']")
</template>

<script setup lang="ts">
import {computed} from 'vue'
import {useI18n} from 'vue-i18n'
import logo from '@/assets/images/common/logo.png'
import {contactInfo} from '@/conf'

const {t} = useI18n()
const currentYear = computed(() => new Date().getFullYear())
</script>

<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
