@use "@/assets/scss/imports.scss" as *;

.layout-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0;
  overflow: hidden;
  box-sizing: border-box;
  background: #fff;
}

.layout-header {
  position: relative;
  z-index: 2;
  height: 40px;
  flex: none;
  background: #236192;
  box-shadow: 0 1px 3px #404040;
}

.layout-main {
  flex: 1;
  display: flex;
}

.layout-left {
  width: 60px;
  //position: fixed;
  //right: 0;
  //top: 0;
  //bottom: 0;
  background: $color-theme;
}

.layout-right {
  flex: 1;
}
