// c-scroll 组件样式
.c-scroll {
  // 滚动条样式定制
  :deep(.el-scrollbar__bar) {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 4px;
      
      &:hover {
        background-color: rgba(144, 147, 153, 0.5);
      }
    }
  }
  
  // RTL语言支持
  &.rtl {
    :deep(.el-scrollbar__wrap) {
      direction: rtl;
    }
  }
  
  // 自定义滚动动画
  :deep(.el-scrollbar__wrap) {
    scroll-behavior: smooth;
  }
} 