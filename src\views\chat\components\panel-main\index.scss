@use "@/assets/scss/imports.scss" as *;
$color-secondary-msg: #979797;
.chat-panel-main-wrapper {
  flex: 1;

  height: 100%;
}
.empty-chat {
  display: flex;

  flex-direction: column;

  justify-content: center;

  align-items: center;

  height: 100%;

  position: relative;
  .logo {
    display: block;

    margin-bottom: 30px;

    width: 100px;
  }
  .title {
    font-size: 30px;

    margin-top: 20px;

    color: $color-theme;
  }
}
.chat-panel-main {
  display: flex;

  position: relative;

  height: 100%;
  .main {
    flex: 1;
    .chat-detail {
      .item {
        display: flex;

        flex-direction: column;

        font-size: 16px;

        background: #ffffff;
        .answer-box,
        .question-box {
          padding: 0 50px 15px 50px;

          border-bottom: #ddd 1px solid;
        }
        .answer-box {
          position: relative;

          background-color: #f2f7ff;

          padding: 0 50px 15px 50px;
        }
        .question-box {
          white-space: pre-line;
        }
        .answer-paginator {
          position: absolute;

          right: 50px;

          top: 10px;

          display: flex;

          align-items: center;

          font-size: 12px;
          i {
            cursor: pointer;

            margin: 0 3px;
            &:hover {
              text-decoration: underline;
            }
          }
          .delimiter {
            margin: 0 3px;
          }
          .total {
            color: $color-secondary-msg;
          }
        }
        .item-top {
          display: flex;

          justify-content: center;

          line-height: 40px;
          .operate-item + .operate-item {
            margin-left: 8px;
          }
          .time {
            text-align: center;

            font-size: 10px;

            color: $color-secondary-msg;
          }
          .iconfont {
            vertical-align: middle;

            cursor: pointer;

            font-size: 16px;

            transition: all 0.3s ease-in-out;

            transition-duration: 0.3s;
          }
          .icon-feedback {
            color: #6fd865;
            &:hover {
              color: $color-theme;
            }
          }
          .icon-ding,
          .icon-cai {
            color: #ccc;
            &:hover {
              color: $color-theme;
            }
          }
          .icon-ding {
            &.active {
              color: #15d445;
            }
          }
          .icon-cai {
            margin-top: 1px;
            &.active {
              color: #f40529;
            }
          }
        }
        .cont {
          display: flex;
          .avatar {
            flex: 0 0 80px;

            margin-right: 30px;

            display: flex;

            flex-direction: column;

            align-items: center;

            img {
              display: block;

              width: 45px;
            }
            .name {
              margin-top: 10px;

              font-size: 12px;

              color: $color-secondary-msg;
            }
          }
          .msg {
            flex: 1;
          }
          .operate {
            text-align: center;

            line-height: 40px;

            height: 40px;

            overflow: hidden;
            :deep(.el-loading-spinner) {
              height: 100%;

              margin-top: -15px;
              .circular {
                width: 20px;

                height: 20px;
              }
            }
            :deep(.operate-item) {
              .iconfont {
                margin: 0 10px;

                cursor: pointer;

                font-size: 20px;

                transition: all 0.3s ease-in-out;

                transition-duration: 0.3s;
              }
              .icon-ding,
              .icon-cai {
                vertical-align: middle;

                color: #ccc;
              }
              &:hover .icon-ding,
              &:hover .icon-cai {
                color: $color-theme;
              }
              .icon-ding {
                &.active {
                  color: #f40529;
                }
              }
              .icon-cai {
                &.active {
                  color: #5b5b5b;
                }
              }
              .count {
                display: inline-block;

                vertical-align: middle;
              }
            }
          }
        }
      }
    }
    .loading-more {
      display: flex;

      justify-content: center;

      align-items: center;

      height: 50px;
    }
    .sapcing-box {
      height: 140px;
    }
    .chat-msg-inhert-origin,
    .chat-msg-type-cursor {
      display: inline;
    }
    .chat-msg-type-cursor {
      opacity: 1;
      &--blink {
        animation: blink 1s infinite;
      }
    }
  }
  .to-bottom {
    position: absolute;

    bottom: 130px;

    right: 20px;

    background-color: $color-theme;

    padding: 8px;

    border-radius: 50%;

    color: #ffffff;

    cursor: pointer;

    opacity: 0.85;

    transition: all 0.3s ease-in-out;

    transition-duration: 0.2s;
    &:hover {
      color: #ffffff;

      opacity: 1;
    }
  }
}

@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-moz-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-ms-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-o-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
