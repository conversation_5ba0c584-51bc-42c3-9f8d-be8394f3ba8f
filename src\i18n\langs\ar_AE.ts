import locale from 'element-plus/es/locale/lang/ar'
// default error message
const DEFAULT_ERROR = 'النظام مشغول. الرجاء معاودة المحاولة في وقت لاحق.'

export default {
  status: {
    DEFAULT_ERROR: DEFAULT_ERROR,
    404: '404 لم يتم العثور على',
    500: DEFAULT_ERROR,
    700: DEFAULT_ERROR,
    1001: 'لا يوجد وجود لمدخل الخدمة API',
    1002: 'لا يوجد الوصول, الرجاء الاتصال بالمدير',
    1011: 'لا يوجد حساب, أو حساب غير صالح. الرجاء تسجيل الدخول مرة أخرى في النظام',
    1012: 'الحساب معطل',
    1013: 'اسم المستخدم أو كلمة المرور غير متطابقة مع بوابة API',
    1101: 'فشل إنشاء رمز التحقق',
    1104: 'خطأ في رمز التحقق الصورة',
    1103: 'تم استخدام رمز التحقق الرسومي',
    1105: 'رمز التحقق الرسومي منتهي الصلاحية'
  },
  ...locale
} 