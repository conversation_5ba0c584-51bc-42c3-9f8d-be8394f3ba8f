/* import the fontawesome core */
import {library} from '@fortawesome/fontawesome-svg-core'

/* import font awesome icon component */
import {FontAwesomeIcon} from '@fortawesome/vue-fontawesome'

/* import specific icons */
import {
  faGithub,
  faTwitter,
  faLinkedin,
  faInstagram,
  faFacebook,
  faTiktok
} from '@fortawesome/free-brands-svg-icons'
import {faGlobe, faShieldAlt, faHeadset, faHelicopter, faPlane, faPlaneUp, faPlaneDeparture} from '@fortawesome/free-solid-svg-icons'

/* add icons to the library */
library.add(
  faGithub,
  faTwitter,
  faLinkedin,
  faInstagram,
  faFacebook,
  faTiktok,
  faGlobe,
  faShieldAlt,
  faHeadset,
  faHelicopter, faPlane, faPlaneUp, faPlaneDeparture
)

export default function install (app: any) {
  app.component('font-awesome-icon', FontAwesomeIcon)
}
