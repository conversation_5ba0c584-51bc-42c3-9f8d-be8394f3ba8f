import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Chat = () => import('@/views/chat/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    name: 'chat',
    path: '/',
    component: DefaultLayout,
    redirect: { name: 'chatIndex' },
    meta: {
      docTitleI18nModule: 'chat',
      ignoreAuth: true
    },
    children: [
      {
        path: '',
        name: 'chatIndex',
        meta: {
          ignoreAuth: false,
          title: 'FleetUp - Chat',
          i18n: 'Chat',
          docTitleI18n: 'default'
        },
        component: Chat
      }
    ]
  })
}
