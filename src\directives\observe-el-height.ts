import type { Directive, DirectiveBinding } from 'vue'
import { useEventStore } from '@/stores/events'

interface ObserveElement extends HTMLElement {
  __onHeightChange__?: () => void
  __observer__?: MutationObserver
}

function throttle(fn: () => void, delay: number): () => void {
  let timer: number | null = null
  return function() {
    if (!timer) {
      timer = window.setTimeout(() => {
        fn()
        timer = null
      }, delay)
    }
  }
}

const observeElHeight: Directive = {
  mounted(el: ObserveElement, binding: DirectiveBinding) {
    const MutationObserver = window.MutationObserver || (window as any).webkitMutationObserver || (window as any).MozMutationObserver
    let recordHeight = ''
    
    const onHeightChange = throttle(function() {
      const height = window.getComputedStyle(el).getPropertyValue('height')
      if (height === recordHeight) {
        return
      }
      recordHeight = height
      
      const emitEventName = binding.value || 'heightChange'
      
      // Option 1: Use Vue's $emit system if component instance is available
      const instance = binding.instance
      if (instance && typeof (instance as any).$emit === 'function') {
        ;(instance as any).$emit(emitEventName, { height, element: el })
      }
      
      // Option 2: Also trigger Pinia EventStore event
      const eventStore = useEventStore()
      eventStore.emitEvent(emitEventName, { height, element: el })
    }, 500)
    
    const immediate = el.getAttribute('observe-immediate')
    if (immediate) {
      onHeightChange()
    }
    
    el.__onHeightChange__ = onHeightChange
    el.__observer__ = new MutationObserver(() => {
      onHeightChange()
    })
    
    el.__observer__.observe(el, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true
    })
  },
  
  unmounted(el: ObserveElement) {
    if (el.__observer__) {
      el.__observer__.disconnect()
      el.__observer__ = undefined
    }
    el.__onHeightChange__ = undefined
  }
}

export default {
  'observe-el-height': observeElHeight
} 