@use "@/assets/scss/imports.scss" as *;
.page-container {
  flex: 1;
  background-size: auto 100px;
}
.footer {
  display: flex;

  align-items: center;
  justify-content: center;
}
:deep(textarea) {
  height: 120px;

  resize: none;

  font-family: inherit;

  word-break: break-word;
}
:deep(.el-alert) {
  padding: 0 0;

  margin-bottom: 5px;

  background: none;
}
.form-display-item {
  border-bottom: #d4e3fc 1px solid;

  position: relative;

  padding: 10px 0;
  .icon {
    width: 20px;

    height: 20px;

    float: left;

    margin-top: -12px;
    img {
      width: 20px;

      height: 20px;
    }
  }
  .text-content {
    min-height: 30px;

    margin-left: 40px;

    line-height: 18px;

    font-size: 14px;

    overflow-y: auto;

    max-height: 60px;

    color: #2a2a2a;

    word-break: break-word;
    p {
      margin: 5px 0;
    }
  }
}
