@use "../utils/constants" as *;

a, blockquote, body, button, caption, dd, div, dl, dt, fieldset, figure, form, h1, h2, h3, h4, h5, h6, hr, html, input, legend, li, menu, ol, p, pre, select, span, table, td, textarea, th, ul {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:focus-visible {
  outline: none;
}

html {
  position: relative;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
}

ol, ul {
  list-style: none;
  -webkit-margin-before: 0;
  -webkit-margin-after: 0;
  -webkit-margin-start: 0;
  -webkit-margin-end: 0;
  -webkit-padding-start: 0;
}

address, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
  box-sizing: border-box;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

caption, th {
  text-align: left;
  font-weight: normal;
}

abbr, body, fieldset, html, iframe, img {
  border: 0;
}

address, cite, dfn, em, i, var {
  font-style: normal;
}

[hidefocus], summary {
  outline: 0;
}

li {
  list-style: none;
}

a {
  color: #409eff;
  text-decoration: none;
}

a:focus, a:hover {
  color: #66b1ff;
}

a:active {
  color: #3a8ee6;
}

h1, h2, h3, h4, h5, h6 {
  color: #606266;
  font-weight: inherit;
}

h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child, p:first-child {
  margin-top: 0;
}

h1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child, p:last-child {
  margin-bottom: 0;
}

h1 {
  font-size: 20px;
}

h2 {
  font-size: 18px;
}

h3 {
  font-size: 16px;
}

h4, h5, h6, p {
  font-size: inherit;
}

p {
  line-height: 1.8;
}

sub, sup {
  font-size: 13px;
}

small {
  font-size: 12px;
}

q:after, q:before {
  content: none;
}

textarea {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  overflow: auto;
  resize: none;
}

label, summary {
  cursor: default;
}

a, button {
  cursor: pointer;
}

b, em, h1, h2, h3, h4, h5, h6, strong {
  font-weight: bold;
}

a, a:hover, del, ins, s, u {
  text-decoration: none;
}

body {
  font-family: Arial, Tahoma;
  font-weight: 400;
  font-size: 14px;
  color: $color-text-default;
  height: 100%;
  position: relative;
  -webkit-font-smoothing: antialiased;
  outline: 0;
}

a {
  color: $SP-LINK-COLOR;

  &:hover {
    color: $SP-LINK-HOVER-COLOR;
  }

  &:active {
    color: $SP-LINK-HOVER-COLOR;
  }
}

img {
  max-width: 100%;
  height: auto;
  width: auto;
  vertical-align: middle;
}

/* 一致化 horizontal rule */
hr {
  border: none;
  border-bottom: 1px solid #cfcfcf;
  margin-bottom: 0.8em;
  height: 10px;
}

/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: rgba(0, 0, 0, 0);
}

/* 定义滚动条轨道 内阴影+圆角 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.3);
  border-radius: 0;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 定义滑块 内阴影+圆角 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.1);
  background-color: rgba(111, 111, 111, 0.3);
}
