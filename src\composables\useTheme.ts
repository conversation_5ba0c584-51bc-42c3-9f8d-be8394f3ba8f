import { ref, onMounted, watch } from 'vue'

// Theme types
type Theme = 'light' | 'dark'

export function useTheme() {
  // Reactive theme state
  const currentTheme = ref<Theme>('light')

  // Initialize theme
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') || 'light'
    currentTheme.value = savedTheme as Theme
    applyTheme(currentTheme.value)
  }

  // Apply theme to DOM
  const applyTheme = (theme: Theme) => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
      // document.body.classList.add('dark-theme')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // Toggle theme
  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', currentTheme.value)
    applyTheme(currentTheme.value)
    return currentTheme.value
  }

  // Set specific theme
  const setTheme = (theme: Theme) => {
    currentTheme.value = theme
    localStorage.setItem('theme', theme)
    applyTheme(theme)
  }

  // Initialize theme when component is mounted
  // onMounted(initTheme)

  // Watch theme changes
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
  })

  return {
    currentTheme,
    toggleTheme,
    setTheme,
    initTheme
  }
}
