import gsap from 'gsap'
import ScrollTrigger from 'gsap/ScrollTrigger'

// 注册所有需要的GSAP插件
gsap.registerPlugin(ScrollTrigger)

// 可以在这里添加一些全局的GSAP配置
// gsap.config({
//   autoSleep: 60,
//   force3D: true,
//   nullTargetWarn: false,
// })

// 导出配置好的gsap实例
export { gsap, ScrollTrigger }

// 创建一些常用的动画预设
export const animations = {
  fadeInUp: {
    opacity: 0,
    y: 30,
    duration: 1,
    ease: 'power2.out'
  },
  fadeInScale: {
    opacity: 0,
    scale: 0.5,
    duration: 0.8,
    ease: 'back.out(1.7)'
  },
  // 可以添加更多预设
} 