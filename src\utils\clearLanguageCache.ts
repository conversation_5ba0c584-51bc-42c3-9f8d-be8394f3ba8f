// Clear the language settings in the browser cache
export function clearLanguageCache() {
  // Clear the language settings in localStorage
  const keysToRemove = [
    'FU_AISEARCH_LANG',
    'FU_AISEARCH_LANG_PACKAGE_ROOT',
    'FU_AISEARCH_HTTP_STATUS_ROOT'
  ]
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key)
  })
  
  // Clear the language settings in sessionStorage
  keysToRemove.forEach(key => {
    sessionStorage.removeItem(key)
  })
  
  // Set the default language to English
  localStorage.setItem('FU_AISEARCH_LANG', 'en_US')
  
  console.log('Language cache cleared, default language set to en_US')
  
  // Suggest the user to refresh the page
  if (window.confirm('Language cache has been cleared, do you want to refresh the page to take effect?')) {
    window.location.reload()
  }
}

// Automatically check and clear invalid language settings
export function autoCheckLanguage() {
  const currentLang = localStorage.getItem('FU_AISEARCH_LANG')
  const validLanguages = ['en_US', 'es_ES', 'pt_PT', 'ar_AE']
  
  if (currentLang && !validLanguages.includes(currentLang)) {
    console.warn(`Invalid language detected: ${currentLang}, clearing cache...`)
    clearLanguageCache()
  }
} 