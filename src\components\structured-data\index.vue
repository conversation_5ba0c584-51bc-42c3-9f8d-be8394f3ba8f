<template>
  <div v-if="jsonLd" v-html="scriptTag" class="hidden" aria-hidden="true"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Breadcrumb {
  name: string
  path: string
}

interface Props {
  type: 'breadcrumb'
  data: {
    breadcrumb?: Breadcrumb[]
    baseUrl?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  type: 'breadcrumb',
  data: () => ({})
})

const jsonLd = computed(() => {
  if (props.type === 'breadcrumb' && props.data.breadcrumb) {
    return JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': props.data.breadcrumb.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${props.data.baseUrl || ''}${item.path}`
      }))
    })
  }
  return null
})

const scriptTag = computed(() => {
  if (!jsonLd.value) return ''
  return `<script type="application/ld+json">${jsonLd.value}<\/script>`
})
</script>

<style scoped>
.hidden {
  display: none;
}
</style> 