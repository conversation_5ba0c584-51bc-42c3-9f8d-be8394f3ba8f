---
description: 
globs: 
alwaysApply: false
---
# FleetUp AI - Search AI and Analytics AI - 项目需求文档

该项目是 FleetUp 在2025年的重要产品升级模块，目标是托管运输车队业务数据，通过自然语言查询提供智能化分析和搜索组件。

---

## 一、产品名称

* **网页标题**：FleetUp AI
* **页面名称选项**：FleetUp AI - Search AI and Analytics AI

---

## 二、路由设计

### 访问途径选项：

* 选项1：顶部栏图标

  * 新增图标：加载在 Add-ons 图标左侧或 Contact Support 图标右侧

* 选项2：Tab Menu Bar

  * 新增图标页Tab，位置可选

    * Settings 的左侧
    * FleetUp Assistant AI 的右侧

* 选项3：直接新增文字页标 Tab

  * Tab 文字可为："AI" / "FleetUp AI" / "Search AI and Analytics AI"
  * Tab 位置选项：

    * Advanced Map 的右侧
    * 最后一个 Drivers Tab 的右侧

> **点击时**：开启新浏览器标签页，打开 FleetUp AI - Search AI and Analytics AI 页面

---

## 三、FleetUp AI 功能模块

### 1. 帐户概览区域 (Account Overview Section)

显示简要运营数据统计：

* **Mobility Volume**：Vehicle / Equipment / Asset / Dashcam 数量
* **User Summary**：Admin / Observer / Driver / Workforce 用户数量 + Geofence 数量

---

### 2. 常见问题 (Common Questions)

* 预置 **Suggested Prompts**，根据帐户行为自动算法推荐
* 请求常用语言库 (e.g. 水油耗量趋势)
* 提示文案：点击问题即填充到 Prompt 输入框

---

### 3. 历史访问记录 (Previous Questions)

* 列表显示过去问题，可重新运行

---

### 4. AI Prompt 指令输入栏

* 支持在 Prompt 输入框中直接输入问题
* 支持通过按钮或键入方式强化产出结果

  * 支持 Output Format 快捷按钮：`Table` / `Chart` / `Excel` / `PDF`

#### 示例1：

```
步骤1：输入问题：Which of my Vehicles have latest timestamp within 1 hour
步骤2：点击 Table 按钮
步骤3：Prompt 被自动补全为：Which of my Vehicles have latest timestamp within 1 hour as a Table.
步骤4：提交问题
步骤5：返回表格结果
```

#### 示例2：

```
步骤1：点击 Table 按钮
步骤2：Prompt 被补全为：As a Table:
步骤3：输入问题继续
步骤4：提交
```

---

### 5. 左侧导航面板 (Left Panel)

* 新对话 (New Chat)
* 历史访问记录 (Chat History)
* 常见问题 (Common Questions)

#### 无论哪个Icon，点击后将同步更新左面并且更换聊天面板类型

---

### 6. 聊天面板 (Chat Panel)

* 显示输入的 Prompt
* 显示 AI 返回的结果（包括多种格式表格、图表、下载链接等）

#### Prompt Input 支持

* 默认 Hint Text
* Output Format 快捷键 (Table / Chart / Excel / PDF)
* Submit 提交按钮

---

### 7. 页底区 Footer

* 输出带 FleetUp AI Guidelines
* 消息 / version / 帐户 logo

---

## 附件

根据已附加的 FleetUp AI Panel Design Overview 图：

* 帐户概览模块
* 常见问题助手
* 历史问题备用
* 指令延伸组件 (Prompt + Table / Chart / Excel / PDF)
