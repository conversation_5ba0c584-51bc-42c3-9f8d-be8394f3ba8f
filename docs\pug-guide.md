# Pug 在 Vue 3 项目中的使用指南

## 简介

Pug（原名 Jade）是一种简洁的模板引擎，可以用来编写更简洁、可读性更高的 HTML 代码。在 Vue 3 项目中使用 Pug 可以使模板更加简洁优雅。

## 安装

我们已经在项目中安装了 Pug 依赖：

```bash
npm install -D pug
```

## 在 Vue 组件中使用 Pug

只需要在 template 标签中添加 `lang="pug"` 属性即可：

```vue
<template lang="pug">
.container
  h1 {{ title }}
  p 这是使用 Pug 语法的模板
</template>
```

## Pug 基本语法

### 1. 缩进表示嵌套

Pug 使用缩进来表示元素的嵌套关系：

```pug
div
  h1 标题
  p 段落
```

生成的 HTML：

```html
<div>
  <h1>标题</h1>
  <p>段落</p>
</div>
```

### 2. 类和 ID

使用 `.` 和 `#` 快速添加类和 ID：

```pug
div.container#main
  h1.title 标题
```

生成的 HTML：

```html
<div class="container" id="main">
  <h1 class="title">标题</h1>
</div>
```

### 3. 元素属性

使用括号添加属性：

```pug
a(href="https://example.com" target="_blank") 点击这里
```

生成的 HTML：

```html
<a href="https://example.com" target="_blank">点击这里</a>
```

### 4. 使用 Vue 指令

在 Pug 中使用 Vue 指令也很简单：

```pug
ul
  li(v-for="item in items" :key="item.id") {{ item.name }}
```

### 5. 在 Pug 中处理 Tailwind CSS 类名

在 Pug 中使用 Tailwind CSS 的类名需要注意以下几点：

- 使用 `.` 连接类名：`.text-red-500.font-bold`
- 特殊字符（如 `:`、`/`）需要使用下划线 `_` 替代：
  - `md:flex` → `md_flex`
  - `w-1/2` → `w-1_2`

例如：

```pug
.container.mx-auto.px-4
  .flex.flex-col.md_flex-row
    .w-full.md_w-1_2.p-4
      h1.text-2xl.font-bold 标题
```

## 推荐的 Pug 书写规范

1. 使用 2 个空格的缩进
2. 标签、类、ID 之间不要加空格：`.container.mx-auto` 而不是 `.container .mx-auto`
3. 属性括号后和内容之间保留一个空格：`a(href="#") 链接`
4. 链接较长的属性时，可以换行：

```pug
button(
  type="button"
  @click="handleClick"
  :disabled="isLoading"
) 提交
```

## 示例

参考项目中的 `src/components/pug-demo/index.vue` 组件作为示例。 