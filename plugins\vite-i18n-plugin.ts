import fs from 'fs'
import path from 'path'
import type { Plugin } from 'vite'

/**
 * Vite Plugin: Merge i18n JSON files
 *
 * This plugin replaces the original gulp task, used to merge multiple JSON files
 * from pre-task/i18n directory into single language files, and output them to public/data/i18n directory
 */
export default function i18nMergePlugin(): Plugin {
  return {
    name: 'vite-i18n-merger',
    enforce: 'pre', // Ensure earliest execution
    apply: 'serve', // Apply during both development and build

    // Execute during configuration phase to ensure earliest file generation
    config(config, { command }) {
      console.log('[i18n] Merging language files in config phase...')
      mergeI18nFiles()
      return config
    },

    // Execute at the start of build
    buildStart() {
      console.log('[i18n] Merging language files...')
      mergeI18nFiles()
    },

    // Execute when development server starts
    configureServer(server) {
      // Execute before server starts to ensure files are ready
      console.log('[i18n] Initial language file merge before server start...')
      mergeI18nFiles()

      // Watch for file changes
      const watcher = server.watcher
      const i18nDir = path.resolve('pre-task/i18n')

      // Use specific event listening instead of all events
      const handleI18nFileChange = (filePath: string) => {
        // Normalize path to ensure cross-platform compatibility
        const normalizedPath = filePath.replace(/\\/g, '/');

        if (normalizedPath.includes('pre-task/i18n') && normalizedPath.endsWith('.json')) {
          console.log(`[i18n] File changed: ${normalizedPath}`);
          mergeI18nFiles();
        }
      };

      // Only watch for the three events we care about
      watcher.on('change', handleI18nFileChange);
      watcher.on('add', handleI18nFileChange);
      watcher.on('unlink', handleI18nFileChange);

      try {
        // Add main directory to watch
        watcher.add(i18nDir);

        // Find all language directories
        const langDirs = fs.readdirSync(i18nDir)
          .filter(file => {
            const fullPath = path.join(i18nDir, file);
            return fs.statSync(fullPath).isDirectory();
          });

        // Add each language directory to watcher
        for (const langDir of langDirs) {
          const langDirPath = path.join(i18nDir, langDir);
          watcher.add(langDirPath);

          // Add all JSON files to watcher
          const jsonFiles = fs.readdirSync(langDirPath)
            .filter(file => file.endsWith('.json'))
            .map(file => path.join(langDirPath, file));

          for (const jsonFile of jsonFiles) {
            watcher.add(jsonFile);
          }
        }

        console.log(`[i18n] Watching i18n directory: ${i18nDir}`);
      } catch (error) {
        console.error('[i18n] Error setting up watchers:', error);
      }
    }
  }
}

/**
 * Main logic for merging i18n files
 */
function mergeI18nFiles() {
  try {
    const baseDir = path.resolve('pre-task/i18n')
    const outDir = path.resolve('public/data/i18n')

    // Ensure output directory exists
    if (!fs.existsSync(outDir)) {
      fs.mkdirSync(outDir, { recursive: true })
    }

    // Get all language folders
    const langDirs = fs.readdirSync(baseDir)
      .filter(file => {
        try {
          return fs.lstatSync(path.join(baseDir, file)).isDirectory()
        } catch (e) {
          return false
        }
      })

    // Process each language folder
    langDirs.forEach(langDir => {
      const langPath = path.join(baseDir, langDir)
      const files = fs.readdirSync(langPath)
        .filter(file => file.endsWith('.json'))
        .map(file => path.join(langPath, file))

      // Merge JSON files
      const mergedData = {}
      for (const file of files) {
        try {
          const fileContent = fs.readFileSync(file, 'utf8')
          const jsonData = JSON.parse(fileContent)
          // Merge objects
          Object.assign(mergedData, jsonData)
        } catch (error) {
          console.error(`[i18n] Error processing file ${file}:`, error)
        }
      }

      // Write merged file
      const outFile = path.join(outDir, `${langDir}.json`)
      fs.writeFileSync(outFile, JSON.stringify(mergedData, null, 2))
      console.log(`[i18n] Created: ${outFile}`)
    })

    console.log('[i18n] Language files merged successfully!')
  } catch (error) {
    console.error('[i18n] Error merging language files:', error)
  }
}
