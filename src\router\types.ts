import 'vue-router'

declare module 'vue-router' {
  interface RouteMeta {
    ignoreAuth?: boolean
    title?: string
    description?: string
    keywords?: string
    image?: string // Open Graph 和 Twitter Card 图片
    type?: string // 页面类型，如 'article', 'product', 'website' 等
    author?: string // 页面作者
    publishedTime?: string // 发布时间，适用于文章类页面
    modifiedTime?: string // 最后修改时间
    locale?: string // 页面语言
    noIndex?: boolean // 是否允许搜索引擎索引
    canonicalUrl?: string // 规范链接
    breadcrumb?: Array<{
      name: string
      path: string
    }> // 面包屑导航
  }
} 