import storage from '@/commons/storage'
import type {MessageSchema, AvailableLocales} from '@/types/i18n.types'
import {useLanguageStore} from '@/stores/language'
import {language} from '@/conf/'
import en_US from './en_US'
// Import other language packs
import es_ES_raw from './es_ES'
import pt_PT_raw from './pt_PT'
import ar_AE_raw from './ar_AE'

// Type conversion
const es_ES = es_ES_raw as unknown as MessageSchema
const pt_PT = pt_PT_raw as unknown as MessageSchema
const ar_AE = ar_AE_raw as unknown as MessageSchema

// All language pack collection
const MESSAGES: Partial<Record<AvailableLocales, MessageSchema>> = {
  en_US,
  es_ES,
  pt_PT,
  ar_AE
}

const DEFAULT_LOCALE: AvailableLocales = language.default as AvailableLocales
// For compatibility, try loading the extension's JSON
const locale = storage.lang as AvailableLocales || DEFAULT_LOCALE

// Dynamically get the language file URL
const getExtendJsonUrl = (locale: AvailableLocales) => `/data/i18n/${locale}.json`

try {
  // Use fetch to get JSON content
  fetch(getExtendJsonUrl(locale))
    .then(response => response.json())
    .then(extendJson => {
      if (MESSAGES[locale]) {
        MESSAGES[locale].app = extendJson
        storage.langPackage = MESSAGES
      }

    })
    .catch(e => {
      console.warn(`Failed to load extended i18n data for ${locale}`, e)
    })
    .finally(() => {
      const languageStore = useLanguageStore()
      languageStore.setLang(locale)
    })
} catch (e) {
  console.warn(`Failed to load extended i18n data for ${locale}`, e)
}

// Store the language configuration package locally for other components to call
storage.langPackage = MESSAGES
// Store the default HTTP status prompt
storage.httpStatus = MESSAGES[locale]?.status || MESSAGES[DEFAULT_LOCALE]!.status

export default MESSAGES as Record<AvailableLocales, MessageSchema>
