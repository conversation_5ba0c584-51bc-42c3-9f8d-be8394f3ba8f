<template lang="pug">
  .layout-wrap
    .layout-header
      HeaderBar
    .layout-main
      .layout-left
        LeftMenuBar
      .layout-right
        router-view(:key="key")
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import storage from '@/commons/storage'
import LeftMenuBar from '@/components/c-right-menu-bar/index.vue'
import HeaderBar from '@/components/c-header/index.vue'
import { useCommonStore } from '@/stores/common'

const route = useRoute()
const { t } = useI18n()
const commonStore = useCommonStore()

// Reactive data - now using Pinia store instead of direct storage access
// Before: const firstLogin = ref<boolean>((storage.get('firstLogin') as unknown as boolean) ?? true)
// Before: ...mapState({ firstLogin: () => storage.firstLogin })
const firstLogin = computed(() => commonStore.firstLogin)

// Computed properties
const key = computed(() => {
  return route.name !== undefined ? String(route.name) + +new Date() : route.path + +new Date()
})

// Methods - now using Pinia store action instead of direct storage manipulation
// Before: ...mapMutations({ updateFirstLogin: TYPES_COMMON.M_COMMON_FIRST_LOGIN })
const updateFirstLogin = (value: boolean) => {
  commonStore.updateFirstLogin(value)
}

const generateMsg = (): string => {
  return `
    <div class="welcome-cont-wrapper">
      <h2 class="title">${t('app.common.welcomeTitle')}</h2>
      <div class="cont">${t('app.common.welcomeMsg')}</div>
    </div>`
}

const showWelcome = async () => {
  const msg = generateMsg()
  try {
    await ElMessageBox.confirm(msg, '', {
      closeOnClickModal: false,
      showCancelButton: false,
      dangerouslyUseHTMLString: true,
      confirmButtonText: t('app.ui.confirm'),
      customClass: 'welcome-wrapper'
    })
  } catch (error) {
    // Handle user cancellation or other errors
    console.log('Welcome dialog cancelled or error:', error)
  }
}

// Lifecycle hooks
onMounted(() => {
  // Show welcome message
  if (firstLogin.value !== false) {
    showWelcome()
    updateFirstLogin(false)
  }
})
</script>

<style lang="scss" scoped>
@use 'index.scss';
</style>
